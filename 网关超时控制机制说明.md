# 网关超时控制机制说明

## 概述

根据您的建议，我们为网关并发控制方案添加了完善的超时机制，确保在MQTT响应超时的情况下能够及时释放锁，避免影响后续请求。

## 超时控制的层次

### 1. 锁获取超时
**控制点**：尝试获取网关锁的等待时间
```java
// 尝试在指定时间内获取锁
boolean acquired = lock.tryLock(timeoutSeconds, TimeUnit.SECONDS);
if (!acquired) {
    throw new RuntimeException("网关[" + gatewayCode + "]获取锁超时");
}
```

### 2. 操作执行超时
**控制点**：MQTT请求和响应的总时间
```java
// 使用Future来控制操作执行的超时
Future<T> future = executor.submit(() -> operation.execute());
return future.get(timeoutSeconds, TimeUnit.SECONDS);
```

### 3. MQTT响应超时
**控制点**：等待MQTT设备响应的时间
```java
// 等待MQTT响应，带超时控制
Message message = future.get(timeoutSeconds, TimeUnit.SECONDS);
```

## 配置参数

### application-dev.yml 配置
```yaml
# 网关请求管理配置
gateway:
  request:
    # 网关请求超时时间（秒）
    timeoutSeconds: 8
    # 网关锁获取超时时间（秒）
    lockTimeoutSeconds: 10
    # 队列最大长度
    maxQueueSize: 100
```

### 配置类 GatewayRequestConfig
```java
@ConfigurationProperties(prefix = "gateway.request")
public class GatewayRequestConfig {
    private Integer timeoutSeconds = 8;        // MQTT请求超时
    private Integer lockTimeoutSeconds = 10;   // 锁获取超时
    private Integer maxQueueSize = 100;        // 队列大小
    private Boolean enableTimeout = true;      // 是否启用超时
}
```

## 超时处理流程

### 场景1：正常情况
```
时间轴: 0s -------- 2s -------- 4s
线程A:  |获取锁|--MQTT请求--|释放锁|
线程B:           |等待锁|--MQTT请求--|
```

### 场景2：MQTT响应超时
```
时间轴: 0s -------- 8s -------- 10s
线程A:  |获取锁|--MQTT超时--|强制释放锁|
线程B:                    |获取锁|--正常执行--|
```

### 场景3：锁获取超时
```
时间轴: 0s -------- 10s -------- 12s
线程A:  |获取锁|----长时间操作----|
线程B:  |等待锁超时|--抛出异常--|
线程C:                      |获取锁|--正常执行--|
```

## 关键实现

### 1. GatewayLockService 超时控制
```java
public <T> T executeWithGatewayLock(String gatewayCode, 
                                   GatewayOperation<T> operation, 
                                   long timeoutSeconds) throws Exception {
    ReentrantLock lock = getGatewayLock(gatewayCode);
    boolean acquired = false;
    
    try {
        // 1. 锁获取超时控制
        acquired = lock.tryLock(timeoutSeconds, TimeUnit.SECONDS);
        if (!acquired) {
            throw new RuntimeException("网关获取锁超时");
        }
        
        // 2. 操作执行超时控制
        ExecutorService executor = Executors.newSingleThreadExecutor();
        Future<T> future = executor.submit(() -> operation.execute());
        return future.get(timeoutSeconds, TimeUnit.SECONDS);
        
    } catch (TimeoutException e) {
        throw new RuntimeException("网关操作执行超时");
    } finally {
        // 3. 确保锁被释放
        if (acquired) {
            lock.unlock();
        }
    }
}
```

### 2. GatewayRequestManager 超时控制
```java
private Map<String, String> sendMqttRequest(PendingRequest request) throws Exception {
    // 发送MQTT请求
    DefaultFuture future = synMqttSender.sendReadMessage(issueTopic, mqttCommand);
    
    // MQTT响应超时控制
    int timeoutSeconds = gatewayRequestConfig.getEffectiveTimeoutSeconds();
    try {
        Message message = future.get(timeoutSeconds, TimeUnit.SECONDS);
        return processResponse(message, request);
    } catch (TimeoutException e) {
        throw new RuntimeException("网关MQTT响应超时");
    }
}
```

## 超时异常处理

### 异常类型
1. **锁获取超时**：`网关[XXX]获取锁超时`
2. **操作执行超时**：`网关[XXX]操作执行超时`
3. **MQTT响应超时**：`网关[XXX]MQTT响应超时`

### 异常处理策略
```java
try {
    Map<String, String> result = gatewayLockService.executeWithGatewayLock(
        gatewayCode, () -> querySingleGatewayData(gatewayCode, itemIds), timeoutSeconds);
    // 处理成功结果
} catch (Exception e) {
    if (e.getMessage().contains("超时")) {
        log.warn("网关[{}]请求超时，跳过该网关: {}", gatewayCode, e.getMessage());
        // 超时不影响其他网关的处理
    } else {
        log.error("网关[{}]请求失败: {}", gatewayCode, e.getMessage(), e);
    }
}
```

## 监控和调试

### 日志输出
```
2024-01-15 10:30:00 INFO  - 网关[GW001]获得锁，开始执行操作
2024-01-15 10:30:08 ERROR - 网关[GW001]MQTT响应超时，超时时间: 8秒
2024-01-15 10:30:08 DEBUG - 网关[GW001]释放锁
2024-01-15 10:30:08 WARN  - 网关[GW001]请求超时，跳过该网关
```

### 性能指标
- 锁获取成功率
- 平均响应时间
- 超时发生频率
- 网关可用性

## 配置建议

### 生产环境配置
```yaml
gateway:
  request:
    timeoutSeconds: 10      # 根据网络环境调整
    lockTimeoutSeconds: 15  # 略大于timeoutSeconds
    maxQueueSize: 200       # 根据并发量调整
    enableTimeout: true
```

### 开发环境配置
```yaml
gateway:
  request:
    timeoutSeconds: 5       # 更短的超时便于测试
    lockTimeoutSeconds: 8
    maxQueueSize: 50
    enableTimeout: true
```

## 测试验证

### 超时测试用例
1. **正常响应测试**：验证在超时时间内的正常处理
2. **MQTT超时测试**：模拟MQTT设备无响应
3. **并发超时测试**：多线程同时访问同一网关
4. **恢复能力测试**：超时后网关锁的正常释放

### 测试命令
```bash
# 运行超时机制测试
mvn test -Dtest=GatewayTimeoutTest

# 运行特定测试方法
mvn test -Dtest=GatewayTimeoutTest#testGatewayLockTimeout
```

## 总结

通过多层次的超时控制机制，我们实现了：

1. **及时释放锁**：避免因MQTT响应超时导致的锁长时间占用
2. **故障隔离**：单个网关的超时不影响其他网关
3. **可配置性**：支持根据环境调整超时参数
4. **可观测性**：完善的日志和监控指标

这个超时机制确保了系统的健壮性和可用性，满足了您提出的需求。
