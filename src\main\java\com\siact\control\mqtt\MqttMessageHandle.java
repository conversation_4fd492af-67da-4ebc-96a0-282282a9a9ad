package com.siact.control.mqtt;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHandler;
import org.springframework.messaging.MessagingException;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Package com.siact.control.mqtt
 * @description:
 * @create 2023/12/15 18:31
 */
@Component
@Slf4j
public class MqttMessageHandle implements MessageHandler {
    @Autowired
    MqttProperties mqttProperties;
    @Autowired
    MqttResHandler mqttResHandler;
    @Override
    public void handleMessage(Message<?> message) throws MessagingException {
        String topic = message.getHeaders().get("mqtt_receivedTopic").toString();
        String gateway = topic.split("/")[2];
        if(topic.endsWith("/result")){
            String msg = message.getPayload().toString();
            JSONObject msgObj = JSONObject.parseObject(msg);
            Integer cmd = msgObj.getInteger("cmd");
            //cmd=20是控制命令的返回消息
            if(cmd==20){
                // ...
                Long cid = msgObj.getLong("cid");
                com.siact.control.mqtt.Message mes = new com.siact.control.mqtt.Message();
                mes.setMessageId(cid);
                mes.setGatewway(gateway);
                mes.setPlayLoad(msg);
                mqttResHandler.deal(mes);
            } else if (cmd==11) {
                //数据读取
                com.siact.control.mqtt.Message mes = new com.siact.control.mqtt.Message();
                mes.setMessageId(99999L);
                mes.setGatewway(gateway);
                mes.setPlayLoad(msg);
                mqttResHandler.deal(mes);
            }
        }
    }
}
