package com.siact.control.utils;


import com.siact.control.mqtt.Message;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.HashSet;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Data
@Slf4j
public class DefaultFuture implements Future {
    // 改为使用String类型的复合键作为Map的key
    private static final Map<String, DefaultFuture> FUTURES = new ConcurrentHashMap<>();
    private final Lock lock = new ReentrantLock();
    private final Condition done = lock.newCondition();
    private int timeout;
    private long id;
    // 新增网关ID字段
    private String gatewayId;
    private final long start = System.currentTimeMillis();
    private boolean cancel = false;
    public final static AtomicLong REQUEST_ID_GEN = new AtomicLong(0);
    private Message msg;

    // 新构造函数，接收网关ID
    public DefaultFuture(String gatewayId, Long id, int timeout) {
        this.gatewayId = gatewayId;
        this.id = id;
        this.timeout = timeout;
        // 使用复合键存储Future
        FUTURES.put(makeKey(gatewayId, id), this);
        log.debug("创建Future: gatewayId={}, messageId={}, key={}", gatewayId, id, makeKey(gatewayId, id));
    }

    // 保留旧构造函数以兼容现有代码
    public DefaultFuture(Long id, int timeout) {
        this("default", id, timeout);
        log.debug("创建Future: messageId={}, 使用默认网关", id);
    }

    // 生成复合键的方法
    private static String makeKey(String gatewayId, Long id) {
        return gatewayId + "_" + id;
    }

    @Override
    public boolean cancel(boolean mayInterruptIfRunning) {
        FUTURES.remove(makeKey(gatewayId, id));
        this.cancel = true;
        return true;
    }

    @Override
    public boolean isCancelled() {
        return cancel;
    }

    @Override
    public boolean isDone() {
        return msg != null;
    }

    @SneakyThrows
    @Override
    public Message get() {
        return get(timeout, TimeUnit.SECONDS);
    }

    @Override
    public Message get(long timeout, TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
        if (timeout <= 0) {
            throw new RuntimeException("参数错误");
        }
        if (!isDone()) {
            long start = System.currentTimeMillis();
            lock.lock();
            try {
                while (!isDone()) {
                    done.await(timeout, TimeUnit.SECONDS);
                    if (isDone() || System.currentTimeMillis() - start > timeout) {
                        break;
                    }
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } finally {
                lock.unlock();
            }
            if (!isDone()) {
                throw new RuntimeException("请求超时");
            }
        }
        return msg;
    }

    private long getStartTimestamp() {
        return start;
    }

    // 检查是否包含指定的复合键
    public static boolean contains(String gatewayId, Long msgId) {
        return FUTURES.containsKey(makeKey(gatewayId, msgId));
    }

    // 保留原有的方法以兼容旧代码
    public static boolean contains(Long msgId) {
        // 遍历所有key，检查是否有任何key以给定的msgId结尾
        for (String key : FUTURES.keySet()) {
            if (key.endsWith("_" + msgId)) {
                return true;
            }
        }
        return false;
    }

    // 接收消息的方法，同时支持新旧格式
    public static void received(Message msg) {
        // 先尝试使用网关ID和消息ID组合查找
        String gatewayId = msg.getGatewway();
        Long messageId = msg.getMessageId();
        DefaultFuture future = null;

        if (gatewayId != null && messageId != null) {
            // 使用复合键查找
            String key = makeKey(gatewayId, messageId);
            future = FUTURES.remove(key);
            log.debug("尝试使用复合键查找Future: key={}, found={}", key, future != null);
        }

        // 如果找不到，尝试只用messageId查找(向后兼容)
        if (future == null && messageId != null) {
            // 收集所有匹配的key
            for (String key : new HashSet<>(FUTURES.keySet())) {
                if (key.endsWith("_" + messageId)) {
                    future = FUTURES.remove(key);
                    log.debug("使用消息ID向后兼容方式查找Future: messageId={}, key={}", messageId, key);
                    break;
                }
            }
        }

        if (future != null) {
            future.doReceived(msg);
        } else {
            log.warn("找不到匹配的Future: gatewayId={}, messageId={}", gatewayId, messageId);
        }
    }

    private void doReceived(Message res) {
        lock.lock();
        try {
            this.msg = res;
            if (done != null) {
                done.signal();
            }
        } finally {
            lock.unlock();
        }
    }

    // 超时扫描线程
    private static class RemotingInvocationTimeoutScan implements Runnable {
        @Override
        public void run() {
            while (true) {
                try {
                    for (Map.Entry<String, DefaultFuture> entry : FUTURES.entrySet()) {
                        DefaultFuture future = entry.getValue();
                        if (future == null || future.isDone()) {
                            continue;
                        }
                        if (System.currentTimeMillis() - future.getStartTimestamp() > future.getTimeout() * 1000) {
                            Message timeoutResponse = new Message();
                            timeoutResponse.setMessageId(future.getId());
                            timeoutResponse.setGatewway(future.getGatewayId());
                            timeoutResponse.setPlayLoad("超时");
                            log.warn("请求超时，自动触发超时响应: gatewayId={}, messageId={}",
                                    future.getGatewayId(), future.getId());
                            DefaultFuture.received(timeoutResponse);
                        }
                    }
                    Thread.sleep(30);
                } catch (Throwable e) {
                    log.error("超时扫描线程异常", e);
                }
            }
        }
    }

    static {
        Thread th = new Thread(new RemotingInvocationTimeoutScan(), "MqttResponseTimeoutScanTimer");
        th.setDaemon(true);
        th.start();
    }
}

