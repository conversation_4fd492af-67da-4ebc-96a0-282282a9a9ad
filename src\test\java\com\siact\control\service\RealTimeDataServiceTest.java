package com.siact.control.service;

import com.siact.control.entity.DeviceModelEntity;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 实时数据服务测试类
 * 用于验证并发查询时ID冲突问题的解决效果
 */
@SpringBootTest
@Slf4j
public class RealTimeDataServiceTest {

    @Autowired
    private RealTimeDataService realTimeDataService;

    /**
     * 测试并发实时数据查询
     */
    @Test
    public void testConcurrentRealTimeQuery() throws InterruptedException {
        int threadCount = 5;  // 并发线程数
        int requestsPerThread = 3;  // 每个线程的请求次数
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        long startTime = System.currentTimeMillis();

        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < requestsPerThread; j++) {
                        // 模拟同一个网关的多次并发请求
                        Map<String, List<String>> gatewayItemIdMap = createTestGatewayMap(threadIndex, j);
                        List<DeviceModelEntity> deviceModels = createTestDeviceModels(threadIndex, j);
                        
                        Map<String, String> result = realTimeDataService.queryRealTimeData(gatewayItemIdMap, deviceModels);
                        
                        log.info("线程{}第{}次请求完成: 获取{}个数据点", 
                                threadIndex, j + 1, result.size());
                        
                        // 模拟请求间隔
                        Thread.sleep(100);
                    }
                } catch (Exception e) {
                    log.error("线程{}执行失败: {}", threadIndex, e.getMessage(), e);
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        executor.shutdown();

        long endTime = System.currentTimeMillis();
        log.info("并发实时数据查询测试完成: {}个线程，每线程{}次请求，总耗时{}ms", 
                threadCount, requestsPerThread, (endTime - startTime));
    }

    /**
     * 测试同一网关的高频并发请求
     */
    @Test
    public void testSameGatewayConcurrentQuery() throws InterruptedException {
        int threadCount = 10;  // 高并发线程数
        String sameGateway = "TEST_GATEWAY_001";  // 使用同一个网关
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        long startTime = System.currentTimeMillis();

        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    // 所有线程都查询同一个网关，模拟高频并发场景
                    Map<String, List<String>> gatewayItemIdMap = new HashMap<>();
                    List<String> itemIds = Arrays.asList("DEVICE_" + threadIndex + "_001", "DEVICE_" + threadIndex + "_002");
                    gatewayItemIdMap.put(sameGateway, itemIds);
                    
                    List<DeviceModelEntity> deviceModels = createTestDeviceModels(threadIndex, 0);
                    
                    Map<String, String> result = realTimeDataService.queryRealTimeData(gatewayItemIdMap, deviceModels);
                    
                    log.info("线程{}查询同一网关[{}]完成: 获取{}个数据点", 
                            threadIndex, sameGateway, result.size());
                            
                } catch (Exception e) {
                    log.error("线程{}查询同一网关失败: {}", threadIndex, e.getMessage(), e);
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        executor.shutdown();

        long endTime = System.currentTimeMillis();
        log.info("同一网关并发查询测试完成: {}个线程查询同一网关，总耗时{}ms", 
                threadCount, (endTime - startTime));
    }

    /**
     * 创建测试用的网关映射
     */
    private Map<String, List<String>> createTestGatewayMap(int threadIndex, int requestIndex) {
        Map<String, List<String>> gatewayItemIdMap = new HashMap<>();
        
        String gatewayCode = "TEST_GATEWAY_" + String.format("%03d", threadIndex);
        List<String> itemIds = Arrays.asList(
                "DEVICE_" + threadIndex + "_" + requestIndex + "_001",
                "DEVICE_" + threadIndex + "_" + requestIndex + "_002",
                "DEVICE_" + threadIndex + "_" + requestIndex + "_003"
        );
        
        gatewayItemIdMap.put(gatewayCode, itemIds);
        return gatewayItemIdMap;
    }

    /**
     * 创建测试用的设备模型
     */
    private List<DeviceModelEntity> createTestDeviceModels(int threadIndex, int requestIndex) {
        List<DeviceModelEntity> deviceModels = new ArrayList<>();
        
        String gatewayCode = "TEST_GATEWAY_" + String.format("%03d", threadIndex);
        
        for (int i = 1; i <= 3; i++) {
            DeviceModelEntity model = new DeviceModelEntity();
            model.setGatewaycode(gatewayCode);
            model.setItemid("DEVICE_" + threadIndex + "_" + requestIndex + "_" + String.format("%03d", i));
            model.setDevProperty("PROP_" + threadIndex + "_" + requestIndex + "_" + String.format("%03d", i));
            deviceModels.add(model);
        }
        
        return deviceModels;
    }
}
