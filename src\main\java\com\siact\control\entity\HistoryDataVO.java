package com.siact.control.entity;/**
 * @Package com.siact.control.entity
 * @description:
 * <AUTHOR>
 * @create 2024/12/26 16:16
 */

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName HistoryDataVO
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/26 16:16
 * @Version 1.0
 **/
@ApiModel("历史数据查询")
@Data
public class HistoryDataVO {
    @ApiModelProperty("数据编码列表")
    private List<String> dataCodeList;
    @ApiModelProperty("时间")
    private String ts;
}
