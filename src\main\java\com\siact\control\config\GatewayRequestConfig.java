package com.siact.control.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 网关请求配置类
 * 管理网关请求相关的超时和队列配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "gateway.request")
public class GatewayRequestConfig {
    
    /**
     * 网关请求超时时间（秒）
     * 包括MQTT发送和响应的总时间
     */
    private Integer timeoutSeconds = 8;
    
    /**
     * 网关锁获取超时时间（秒）
     * 等待获取网关锁的最大时间
     */
    private Integer lockTimeoutSeconds = 10;
    
    /**
     * 队列最大长度
     * 每个网关请求队列的最大容量
     */
    private Integer maxQueueSize = 100;
    
    /**
     * 是否启用超时控制
     */
    private Boolean enableTimeout = true;
    
    /**
     * 超时重试次数
     */
    private Integer retryCount = 0;
    
    /**
     * 获取有效的超时时间
     * @return 超时时间（秒）
     */
    public int getEffectiveTimeoutSeconds() {
        return timeoutSeconds != null && timeoutSeconds > 0 ? timeoutSeconds : 8;
    }
    
    /**
     * 获取有效的锁超时时间
     * @return 锁超时时间（秒）
     */
    public int getEffectiveLockTimeoutSeconds() {
        return lockTimeoutSeconds != null && lockTimeoutSeconds > 0 ? lockTimeoutSeconds : 10;
    }
    
    /**
     * 获取有效的队列大小
     * @return 队列大小
     */
    public int getEffectiveMaxQueueSize() {
        return maxQueueSize != null && maxQueueSize > 0 ? maxQueueSize : 100;
    }
}
