package com.siact.control.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siact.control.entity.*;
import com.siact.control.enume.ControlPathEnum;
import com.siact.control.exception.ErrorCode;
import com.siact.control.exception.ServerException;
import com.siact.control.mqtt.Message;
import com.siact.control.mqtt.SynMqttSender;
import com.siact.control.utils.DateUtils;
import com.siact.control.utils.DefaultFuture;
import com.siact.control.utils.Result;
import com.siact.control.utils.TaskUtil;
import javafx.concurrent.Task;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Package com.siact.control.service
 * @description:
 * @create 2023/4/4 11:11
 */
@Slf4j
@Service
public class StrategyService {
    @Autowired
    CommandDetailService commandDetailService;
    @Autowired
    DeviceModelService deviceModelService;
    @Autowired
    SynMqttSender synMqttSender;
    @Autowired
    AsyncTaskExecutor asyncTaskExecutor;
    @Autowired
    TDengineService tdengineService;
    @Autowired
    GatewayLockService gatewayLockService;
    @Autowired
    GatewayRequestManager gatewayRequestManager;
    @Value("${mqtt.readTimeout}")
    private int readTimeout;
    @Value("${mqtt.totalTimeout}")
    private int totalTimeout;
    /**
     * 发布topic前缀
     */
    @Value("${mqtt.prefixTopic}")
    private String prefixTopic;

    /**
     * 发布topic后缀
     */
    @Value("${mqtt.suffixTopic}")
    private String suffixTopic;

    @Value("${isTest}")
    private boolean isTest;


    /**
     * 发送MQTT读取请求并处理响应
     */
    private Map<String, String> issueReadMqtt(HashMap<String, JSONObject> gatewayCommandMap) {
        if (gatewayCommandMap == null || gatewayCommandMap.isEmpty()) {
            log.warn("没有网关命令需要处理");
            return new HashMap<>();
        }

        log.info("开始向{}个网关发送读取请求", gatewayCommandMap.size());
//        Map<String, DefaultFuture> futureMap = new HashMap<>();
        List<DefaultFuture> futureList = new ArrayList<>();

        // 发送MQTT消息到各网关
        for (Map.Entry<String, JSONObject> entry : gatewayCommandMap.entrySet()) {
            String gatewaycode = entry.getKey();
            JSONObject command = entry.getValue();
            String issueTopic = getIssueTopic(gatewaycode);

            try {
                log.info("向网关[{}]发送MQTT读取请求: topic={}, command={}",
                        gatewaycode, issueTopic, command.toJSONString());

                DefaultFuture defaultFuture = synMqttSender.sendReadMessage(issueTopic, command);
                futureList.add(defaultFuture);
                log.info("网关[{}]MQTT请求已发送，等待响应", gatewaycode);
            } catch (Exception e) {
                log.error("向网关[{}]发送MQTT请求失败: {}", gatewaycode, e.getMessage(), e);
                // 记录失败但继续处理其他网关
            }
        }

        log.info("所有MQTT请求已发送，共{}个，开始处理响应", futureList.size());
        return processReadResponse(futureList);
    }

    /**
     * 处理所有网关的响应
     */
    /**
     * 处理所有网关的响应（并行优化版）
     */
    /**
     * 处理所有网关的响应（并行优化版）
     */
    private Map<String, String> processReadResponse(List<DefaultFuture> futureList) {
        if (futureList.isEmpty()) {
            log.info("无网关响应需要处理");
            return new HashMap<>();
        }

        log.info("开始并行处理{}个网关的响应", futureList.size());
        Map<String, String> resultMap = new ConcurrentHashMap<>(); // 使用线程安全的Map

        log.info("单个网关超时时间为{}秒，总体响应等待时间为{}秒", readTimeout, totalTimeout);

        // 创建并行任务
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);

        // 为每个网关创建一个并行处理任务
        for (DefaultFuture future : futureList) {
            CompletableFuture<Void> processFuture = CompletableFuture.runAsync(() -> {

                try {
                    // 使用DefaultFuture自身的超时机制，不指定额外超时
                    Message message = future.get();

                    if (message == null) {
                        failureCount.incrementAndGet();
                        return;
                    }
                    String gateway = message.getGatewway();
                    String payload = message.getPlayLoad();
                    log.info("网关[{}]原始响应: {}", gateway, payload);

                    if ("超时".equals(payload)) {
                        log.warn("网关[{}]响应超时", gateway);
                        failureCount.incrementAndGet();
                        cleanupGatewayMappings(gateway);
                        return;
                    }

                    // 解析JSON响应
                    JSONObject resultObj = JSONObject.parseObject(payload);
                    log.info("网关[{}]响应解析成功", gateway);

                    // 处理该网关的响应数据
                    processGatewayResponse(resultObj, gateway, resultMap);
                    successCount.incrementAndGet();

                } catch (Exception e) {
                    log.error("处理网关响应时发生异常: {}", e.getMessage(), e);
//                    cleanupGatewayMappings(gateway);
                    failureCount.incrementAndGet();
                }
            }, asyncTaskExecutor);

            futures.add(processFuture);
        }

        try {
            // 等待所有任务完成或总体超时
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0]));

            // 设置总体超时（略长于单个请求超时）
            allFutures.get(totalTimeout, TimeUnit.SECONDS);
            log.info("所有并行任务已完成");
            TaskUtil.iotReadPropMap.clear();
        } catch (TimeoutException e) {
            log.warn("等待网关响应超过总体超时时间{}秒，将处理已完成的响应", totalTimeout);
        } catch (Exception e) {
            log.error("等待网关响应过程中发生异常: {}", e.getMessage(), e);
        }

        log.info("所有网关处理完成: 成功{}个, 失败{}个, 共获取{}个数据点",
                successCount.get(), failureCount.get(), resultMap.size());

        return new HashMap<>(resultMap); // 转换为普通HashMap返回
    }

    /**
     * 处理单个网关的响应数据
     */
    private void processGatewayResponse(JSONObject msgResult, String gatewaycode,
                                        Map<String, String> resultMap) {
        try {
            // 获取data字段的字符串值
            String dataStr = msgResult.getString("data");
            if (dataStr == null) {
                log.warn("网关[{}]响应中缺少data字段", gatewaycode);
                return;
            }

            // 解析JSON数组
            JSONArray dataArray = JSONArray.parseArray(dataStr);
            log.info("网关[{}]响应包含{}个设备数据项", gatewaycode, dataArray.size());

            ArrayList<TDDataEntity> tdDataEntityList = new ArrayList<>();
            StringBuilder dataLogBuilder = new StringBuilder();
            dataLogBuilder.append(String.format("网关[%s]设备数据详情:\n", gatewaycode));

            int processedCount = 0;
            int skippedCount = 0;

            // 遍历数组，提取MESdevid和DataValue
            for (int i = 0; i < dataArray.size(); i++) {
                JSONObject item = dataArray.getJSONObject(i);
                String mesDevId = item.getString("MESdevid");
                String dataValue = item.getString("DataValue");

                String mapKey = gatewaycode + "_" + mesDevId;
                String propCode = TaskUtil.iotReadPropMap.getOrDefault(mapKey, null);

                if (StrUtil.isNotBlank(propCode)) {
                    resultMap.put(propCode, dataValue);

                    // 处理完成后移除映射
                    TaskUtil.iotReadPropMap.remove(mapKey);

                    // 构建TDengine数据对象
                    TDDataEntity tdEngineData = TDDataEntity.builder()
                            .ts(DateUtils.getCurrentimeStr())         // 时间戳
                            .gatewayCode(gatewaycode)              // 网关编码
                            .itemid(mesDevId)                    // 设备ID
                            .devproperty(propCode)                // 属性编码
                            .itemvalue(dataValue)                      // 属性值
                            .build();

                    tdDataEntityList.add(tdEngineData);

                    // 添加到日志
                    dataLogBuilder.append(String.format("  - 设备[%s] 属性[%s] 值[%s]\n",
                            mesDevId, propCode, dataValue));
                    processedCount++;
                } else {
                    dataLogBuilder.append(String.format("  - 设备[%s] 未找到映射，已跳过\n", mesDevId));
                    skippedCount++;
                }
            }

            // 记录设备数据详情
            log.info(dataLogBuilder.toString());
            log.info("网关[{}]处理结果: 成功处理{}个设备数据，跳过{}个未映射设备",
                    gatewaycode, processedCount, skippedCount);

            // 异步存储数据到TDengine
            if (!tdDataEntityList.isEmpty()) {
                log.info("网关[{}]开始异步存储{}个数据点到TDengine", gatewaycode, tdDataEntityList.size());
                asyncStoreToTDengine(tdDataEntityList);
            }

        } catch (Exception e) {
            log.error("解析网关[{}]MQTT消息失败: {}", gatewaycode, e.getMessage(), e);
            // 异常时也要确保清理映射
            cleanupGatewayMappings(gatewaycode);
            // 重新抛出异常，让上层方法捕获并继续处理其他网关
            throw e;
        }
    }

    private Map<String, String> handlerReadResult(JSONObject msgResult, String gatewaycode, Map<String, String> dataCodeValDTOList) {

        try {
            // 获取data字段的字符串值
            String dataStr = msgResult.getString("data");
            // 因为data是一个字符串形式的JSON数组，需要先解析成JSONArray
            JSONArray dataArray = JSONArray.parseArray(dataStr);
            ArrayList<TDDataEntity> tdDataEntityList = new ArrayList<>();
            // 遍历数组，提取MESdevid和DataValue
            for (int i = 0; i < dataArray.size(); i++) {
                JSONObject item = dataArray.getJSONObject(i);
                String mesDevId = item.getString("MESdevid");
                String dataValue = item.getString("DataValue");
                if (StrUtil.isNotBlank(TaskUtil.iotReadPropMap.getOrDefault(gatewaycode + "_" + mesDevId, null))) {
                    String propCode = TaskUtil.iotReadPropMap.get(gatewaycode + "_" + mesDevId);
                    dataCodeValDTOList.put(propCode, dataValue);
                    TaskUtil.iotReadPropMap.remove(gatewaycode + "_" + mesDevId);
                    // 构建TDengine数据对象
                    TDDataEntity tdEngineData = TDDataEntity.builder()
                            .ts(DateUtils.getCurrentimeStr())         // 时间戳
                            .gatewayCode(gatewaycode)              // 网关编码
                            .itemid(mesDevId)                    // 设备ID
                            .devproperty(propCode)                // 属性编码
                            .itemvalue(dataValue)                      // 属性值
                            .build();

                    tdDataEntityList.add(tdEngineData);
                }
            }

            // 将数据加入TDengine缓存队列，由定时任务批量插入
            if (!tdDataEntityList.isEmpty()) {
                try {
                    tdengineService.addToCacheQueue(tdDataEntityList);
                    log.debug("已将{}条数据加入TDengine缓存队列", tdDataEntityList.size());
                } catch (Exception e) {
                    log.error("加入TDengine缓存队列失败: {}", e.getMessage(), e);
                    // 缓存失败不影响主流程
                }
            }


        } catch (Exception e) {
            log.error("解析MQTT消息失败: {}", e.getMessage(), e);
            throw new RuntimeException("数据解析失败", e);
        }
        return dataCodeValDTOList;
    }

    /**
     * 处理MQTT返回结果
     */
    /**
     * 处理MQTT返回结果
     */
    private ControlResultVo handlerResult(JSONObject msgResult, String gatewaycode, IssueCommandVo originalCommand) {
        ControlResultVo resultVo = new ControlResultVo();
        List<DeviceControlResult> singleResults = new ArrayList<>();
        List<List<DeviceControlResult>> multiResults = new ArrayList<>();

        if (!ObjectUtils.isEmpty(msgResult)) {
            // 直接处理 cmdstate,不考虑 rtype
            String cmdstate = msgResult.getString("cmdstate");
            if (StringUtils.isNotBlank(cmdstate)) {
                // 创建一个Map来存储所有执行结果
                Map<String, String> resultMap = new HashMap<>();

                String[] split = cmdstate.split("/");
                for (String pointStatus : split) {
                    if (StringUtils.isBlank(pointStatus)) continue;

                    String[] pointValue = pointStatus.split(":");
                    if (pointValue.length > 1) {
                        String tagId = pointValue[0];
                        String exeStatus = pointValue[1];

                        // 获取网关+点位对应的模型
                        String propCode = TaskUtil.iotPropMap.get(gatewaycode + "_" + tagId);
                        if (StringUtils.isNotBlank(propCode)) {
                            // 将结果存入Map
                            resultMap.put(propCode, "True".equalsIgnoreCase(exeStatus) ? "true" : "false");
                            // 移除已处理的映射
                            TaskUtil.iotPropMap.remove(gatewaycode + "_" + tagId);
                        }
                    }
                }

                // 处理单点控制结果
                if (!ObjectUtil.isEmpty(originalCommand.getIssueDetail())) {
                    for (DeviceControl control : originalCommand.getIssueDetail()) {
                        DeviceControlResult result = new DeviceControlResult();
                        result.setPropCode(control.getPropCode());
                        // 如果在resultMap中找不到结果,默认为false
                        result.setResult(resultMap.getOrDefault(control.getPropCode(), "false"));
                        singleResults.add(result);
                    }
                }

                // 处理组合控制结果
                if (!ObjectUtil.isEmpty(originalCommand.getMultiIssueDetail())) {
                    for (List<DeviceControl> group : originalCommand.getMultiIssueDetail()) {
                        List<DeviceControlResult> groupResults = new ArrayList<>();
                        for (DeviceControl control : group) {
                            DeviceControlResult result = new DeviceControlResult();
                            result.setPropCode(control.getPropCode());
                            // 如果在resultMap中找不到结果,默认为false
                            result.setResult(resultMap.getOrDefault(control.getPropCode(), "false"));
                            groupResults.add(result);
                        }
                        multiResults.add(groupResults);
                    }
                }
            } else {
                // 如果cmdstate为空,所有点位设置为false
                setAllResultsToFalse(originalCommand, singleResults, multiResults);
            }
        } else {
            // 如果msgResult为空,所有点位设置为false
            setAllResultsToFalse(originalCommand, singleResults, multiResults);
        }

        resultVo.setIssueDetail(singleResults);
        resultVo.setMultiIssueDetail(multiResults);
        return resultVo;
    }

    // 辅助方法:将所有结果设置为false
    private void setAllResultsToFalse(IssueCommandVo originalCommand,
                                      List<DeviceControlResult> singleResults,
                                      List<List<DeviceControlResult>> multiResults) {
        // 处理单点控制结果
        if (!ObjectUtil.isEmpty(originalCommand.getIssueDetail())) {
            for (DeviceControl control : originalCommand.getIssueDetail()) {
                DeviceControlResult result = new DeviceControlResult();
                result.setPropCode(control.getPropCode());
                result.setResult("false");
                singleResults.add(result);
            }
        }

        // 处理组合控制结果
        if (!ObjectUtil.isEmpty(originalCommand.getMultiIssueDetail())) {
            for (List<DeviceControl> group : originalCommand.getMultiIssueDetail()) {
                List<DeviceControlResult> groupResults = new ArrayList<>();
                for (DeviceControl control : group) {
                    DeviceControlResult result = new DeviceControlResult();
                    result.setPropCode(control.getPropCode());
                    result.setResult("false");
                    groupResults.add(result);
                }
                multiResults.add(groupResults);
            }
        }
    }

    public String getIssueTopic(String gatewaycode) {
        return prefixTopic + gatewaycode + suffixTopic;
    }


    /**
     * 将设备控制列表转换为网关和点位的映射
     * 将JSONArray commandList转化为 ===》<devCode + "α" + propCode,value> ===》  为<gateway,<point,value>>
     */
    public Map<String, Map<String, List<DeviceControl>>> getDeviceControlMap(List<DeviceControl> singleIssueDetail, List<List<DeviceControl>> multiIssueDetail) {
        Map<String, Map<String, List<DeviceControl>>> gatewayPointMap = new HashMap<>();

        // 处理单点控制
        if (!ObjectUtil.isEmpty(singleIssueDetail)) {
            handleSingleControl(singleIssueDetail, gatewayPointMap);
        }

        // 处理组合控制
        if (!ObjectUtil.isEmpty(multiIssueDetail)) {
            handleMultiControl(multiIssueDetail, gatewayPointMap);
        }

        return gatewayPointMap;
    }


    /**
     * 处理单点控制
     */
    private void handleSingleControl(List<DeviceControl> singleIssueDetail, Map<String, Map<String, List<DeviceControl>>> gatewayPointMap) {
        for (DeviceControl control : singleIssueDetail) {
            DeviceModelEntity model = TaskUtil.entityMap.get(control.getPropCode());
            if (model != null) {
                String gateway = model.getGatewaycode();

                // 将单点控制放入map中，使用单独的List存储
                gatewayPointMap.computeIfAbsent(gateway, k -> new HashMap<>())
                        .computeIfAbsent("single", k -> new ArrayList<>())
                        .add(control);

                // 记录propCode和itemid的对应关系，用于后续结果处理
                TaskUtil.iotPropMap.put(gateway + "_" + model.getItemid(), control.getPropCode());
            }
        }
    }

    /**
     * 处理组合控制
     */
    private void handleMultiControl(List<List<DeviceControl>> multiIssueDetail, Map<String, Map<String, List<DeviceControl>>> gatewayPointMap) {
        for (int i = 0; i < multiIssueDetail.size(); i++) {
            List<DeviceControl> controlGroup = multiIssueDetail.get(i);
            if (ObjectUtil.isEmpty(controlGroup)) continue;

            // 确保同一组的控制点位属于同一个网关
            DeviceModelEntity firstModel = TaskUtil.entityMap.get(controlGroup.get(0).getPropCode());
            if (firstModel == null) continue;

            String gateway = firstModel.getGatewaycode();

            // 将组合控制放入map中，使用组号作为key
            gatewayPointMap.computeIfAbsent(gateway, k -> new HashMap<>())
                    .put("group_" + i, controlGroup);

            // 记录每个点位的propCode，用于后续结果处理
            for (DeviceControl control : controlGroup) {
                DeviceModelEntity model = TaskUtil.entityMap.get(control.getPropCode());
                if (model != null) {
                    TaskUtil.iotPropMap.put(gateway + "_" + model.getItemid(), control.getPropCode());
                }
            }
        }
    }


    /**
     * 组装MQTT消息并发送
     */
    public Result issueMessage(Long commandId, Map<String, Map<String, List<DeviceControl>>> deviceControlMap, IssueCommandVo originalCommand) {
        if (deviceControlMap.isEmpty()) {
            return Result.error(ErrorCode.MODEL_NOT_MATCH);
        }

        // 记录需要等待返回的网关数量
        TaskUtil.commandid2gatesizeMap.put(commandId, deviceControlMap.size());

        // 遍历每个网关发送消息
        for (Map.Entry<String, Map<String, List<DeviceControl>>> gatewayEntry : deviceControlMap.entrySet()) {
            String gateway = gatewayEntry.getKey();
            Map<String, List<DeviceControl>> controlGroups = gatewayEntry.getValue();

            // 组装MQTT消息
            JSONObject mqttMsg = new JSONObject();
            mqttMsg.put("cmd", ControlPathEnum.CMD_CONTROL.code());
            mqttMsg.put("mode", ControlPathEnum.MQTT.code());
            mqttMsg.put("cid", commandId);

            JSONArray tagArray = new JSONArray();

            // 处理所有控制组
            for (Map.Entry<String, List<DeviceControl>> groupEntry : controlGroups.entrySet()) {
                if (groupEntry.getKey().equals("single")) {
                    // 处理单点控制
                    for (DeviceControl control : groupEntry.getValue()) {
                        DeviceModelEntity model = TaskUtil.entityMap.get(control.getPropCode());
                        if (model != null) {
                            JSONObject tagObj = new JSONObject();
                            tagObj.put("dataMode", ControlPathEnum.SINGLE_CONTROL.code());
                            tagObj.put("tagpos", model.getItemid());
                            tagObj.put("dataValue", control.getTargetValue());
                            tagArray.add(tagObj);
                        }
                    }
                } else {
                    // 处理组合控制
                    List<DeviceControl> controlGroup = groupEntry.getValue();
                    // 按前缀分组并按后缀排序
                    Map<String, List<DeviceControl>> groupedControls = new HashMap<>();

                    for (DeviceControl control : controlGroup) {
                        DeviceModelEntity model = TaskUtil.entityMap.get(control.getPropCode());
                        if (model != null) {
                            String itemId = model.getItemid();
                            String prefix = itemId.substring(0, itemId.lastIndexOf('_'));
                            int suffix = Integer.parseInt(itemId.substring(itemId.lastIndexOf('_') + 1));

                            groupedControls.computeIfAbsent(prefix, k -> new ArrayList<>()).add(control);
                        }
                    }

                    // 处理每个组
                    for (Map.Entry<String, List<DeviceControl>> entry : groupedControls.entrySet()) {
                        List<DeviceControl> controls = entry.getValue();

                        // 按后缀排序
                        controls.sort(Comparator.comparingInt(control -> {
                            String itemId = TaskUtil.entityMap.get(control.getPropCode()).getItemid();
                            return Integer.parseInt(itemId.substring(itemId.lastIndexOf('_') + 1));
                        }));

                        // 如果有缺失点位，则分为子组
                        List<List<DeviceControl>> subgroups = new ArrayList<>();
                        List<DeviceControl> currentGroup = new ArrayList<>();
                        int expectedSuffix = -1;

                        for (DeviceControl control : controls) {
                            String itemId = TaskUtil.entityMap.get(control.getPropCode()).getItemid();
                            int suffix = Integer.parseInt(itemId.substring(itemId.lastIndexOf('_') + 1));

                            if (expectedSuffix != -1 && suffix != expectedSuffix) {
                                subgroups.add(new ArrayList<>(currentGroup));
                                currentGroup.clear();
                            }

                            currentGroup.add(control);
                            expectedSuffix = suffix + 1;
                        }

                        if (!currentGroup.isEmpty()) {
                            subgroups.add(currentGroup);
                        }

                        // 处理每个子组
                        for (List<DeviceControl> subgroup : subgroups) {
                            JSONObject tagObj = new JSONObject();
                            tagObj.put("dataMode", ControlPathEnum.COMBINATION_CONTROL.code()); // 组合控制的值

                            List<String> tagpos = new ArrayList<>();
                            List<String> dataValues = new ArrayList<>();

                            for (DeviceControl control : subgroup) {
                                DeviceModelEntity model = TaskUtil.entityMap.get(control.getPropCode());
                                if (model != null) {
                                    tagpos.add(model.getItemid());
                                    dataValues.add(control.getTargetValue());
                                }
                            }

                            tagObj.put("tagpos", String.join("/", tagpos));
                            tagObj.put("dataValue", String.join("/", dataValues));
                            tagArray.add(tagObj);
                        }
                    }
                }
            }

            mqttMsg.put("tag", tagArray);

            // 发送MQTT消息
            try {
                // 无论是否是测试模式，都发送MQTT消息
                String topic = getIssueTopic(gateway);
                DefaultFuture future = synMqttSender.sendMessage(topic, mqttMsg);

                if (isTest) {
                    // 测试模式：不变
                    log.info("测试模式 - MQTT控制指令已发送: topic={}, message={}", topic, mqttMsg.toJSONString());
                    JSONObject mockResult = generateMockResult(mqttMsg);
                    ControlResultVo resultVo = handlerResult(mockResult, gateway, originalCommand);
                    return Result.ok(resultVo);
                } else {
                    // 正常模式：等待实际返回结果
                    try {
                        Message message = future.get();
                        if (message == null) {
                            log.warn("网关[{}]控制请求超时，命令ID={}", gateway, commandId);
                            return Result.error(ErrorCode.REQUEST_TIMEOUT.getCode(), "请求超时");
                        }
                        JSONObject msgResult = JSONObject.parseObject(message.getPlayLoad());
                        ControlResultVo resultVo = handlerResult(msgResult, gateway, originalCommand);
                        return Result.ok(resultVo);
                    } catch (RuntimeException e) {
                        // 明确处理超时异常
                        if (e.getMessage() != null && e.getMessage().contains("请求超时")) {
                            log.warn("网关[{}]控制请求超时，命令ID={}", gateway, commandId);
                            return Result.error(ErrorCode.REQUEST_TIMEOUT.getCode(), "请求超时");
                        } else {
                            // 其他运行时异常
                            throw e;
                        }
                    }
                }
            } catch (Exception e) {
                // 只有真正的消息发送失败或其他非超时异常才会到这里
                log.error("向网关[{}]发送MQTT消息失败，命令ID={}", gateway, commandId, e);
                return Result.error(ErrorCode.COMM_EXCEPTION.getCode(), "通信异常");
            }
        }
        return Result.ok();
    }

    /**
     * 生成模拟返回结果
     */
    private JSONObject generateMockResult(JSONObject mqttMsg) {
        JSONObject mockResult = new JSONObject();
        mockResult.put("cid", mqttMsg.getString("cid"));
        mockResult.put("cmd", mqttMsg.getInteger("cmd"));

        // 随机生成整体执行状态
        Random random = new Random();
        boolean overallSuccess = random.nextBoolean();
        mockResult.put("rtype", overallSuccess ? "True" : "False");

        if (overallSuccess) {
            // 如果整体执行成功，为每个点位生成随机状态
            StringBuilder cmdstate = new StringBuilder();
            JSONArray tags = mqttMsg.getJSONArray("tag");

            for (int i = 0; i < tags.size(); i++) {
                JSONObject tag = tags.getJSONObject(i);
                if (tag.getInteger("dataMode") == 1) {
                    // 单点控制
                    String tagpos = tag.getString("tagpos");
                    cmdstate.append("/").append(tagpos).append(":").append(random.nextBoolean() ? "True" : "False");
                } else {
                    // 组合控制
                    String[] tagposArray = tag.getString("tagpos").split("/");
                    for (String tagpos : tagposArray) {
                        cmdstate.append("/").append(tagpos).append(":").append(random.nextBoolean() ? "True" : "False");
                    }
                }
            }
            mockResult.put("cmdstate", cmdstate.toString());
        } else {
            // 如果整体执行失败，返回空的cmdstate
            mockResult.put("cmdstate", "");
        }

        log.info("测试模式 - 生成模拟返回结果: {}", mockResult.toJSONString());
        return mockResult;
    }

    /**
     * 根据数据编码列表获取网关项目ID映射
     *
     * @param dataCodeList 数据编码列表
     * @return 网关编码到项目ID列表的映射
     * @throws ServerException 当没有匹配的设备模型时抛出
     */
    public Map<String, List<String>> getGatewayItemIdMap(List<String> dataCodeList) {

        // 参数校验
        if (CollectionUtil.isEmpty(dataCodeList)) {
            throw new ServerException(ErrorCode.MODEL_NOT_MATCH);
        }
        // 查询设备模型
        List<DeviceModelEntity> deviceModels = queryDeviceModels(dataCodeList);
        // 构建映射关系（不再使用全局映射）
        return buildGatewayItemIdMap(deviceModels);
    }

    /**
     * 查询设备模型
     */
    private List<DeviceModelEntity> queryDeviceModels(List<String> dataCodeList) {
        QueryWrapper<DeviceModelEntity> wrapper = new QueryWrapper<>();
        wrapper.in("dev_property", dataCodeList);

        List<DeviceModelEntity> deviceModels = deviceModelService.list(wrapper);
        if (CollectionUtil.isEmpty(deviceModels)) {
            throw new ServerException(ErrorCode.MODEL_NOT_MATCH);
        }
        return deviceModels;
    }

    /**
     * 构建网关到项目ID的映射
     */
    private Map<String, List<String>> buildGatewayItemIdMap(List<DeviceModelEntity> deviceModels) {
        // 不再使用全局映射，改为在响应处理时直接查找
        return deviceModels.stream()
                .filter(model -> StringUtils.isNotBlank(model.getGatewaycode())
                        && StringUtils.isNotBlank(model.getItemid()))
                .collect(Collectors.groupingBy(
                        DeviceModelEntity::getGatewaycode,
                        Collectors.mapping(DeviceModelEntity::getItemid, Collectors.toList())
                ));
    }

    /**
     * 使用网关锁机制处理实时数据查询，解决并发ID冲突问题
     */
    public Map<String, String> issueRealTimeDataMessage(Map<String, List<String>> gatewayItemIdMap) {
        if (gatewayItemIdMap == null || gatewayItemIdMap.isEmpty()) {
            log.warn("没有网关项目ID映射数据，无法发送MQTT消息");
            return new HashMap<>();
        }

        // 根据测试模式选择处理方法
        if (isTest) {
            log.info("以测试模式发送MQTT请求");
            // 构建测试用的命令映射
            HashMap<String, JSONObject> gatewayCommandMap = new HashMap<>();
            gatewayItemIdMap.forEach((gateway, itemIds) -> {
                JSONObject mqttCommJObject = new JSONObject();
                mqttCommJObject.put("cmd", ControlPathEnum.CMD_READ.code());
                JSONArray tags = new JSONArray();
                for (String itemId : itemIds) {
                    tags.add(itemId);
                }
                mqttCommJObject.put("tagID", tags);
                gatewayCommandMap.put(gateway, mqttCommJObject);
            });
            return issueReadMqttMONI(gatewayCommandMap);
        } else {
            log.info("以正常模式使用网关请求管理器处理并发请求");
            return processRealTimeDataWithRequestManager(gatewayItemIdMap);
        }
    }

    /**
     * 使用网关请求管理器处理实时数据查询（推荐方案）
     */
    private Map<String, String> processRealTimeDataWithRequestManager(Map<String, List<String>> gatewayItemIdMap) {
        Map<String, String> resultMap = new ConcurrentHashMap<>();
        List<CompletableFuture<Map<String, String>>> futures = new ArrayList<>();

        // 查询所有相关的设备模型
        List<String> allItemIds = gatewayItemIdMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
        List<DeviceModelEntity> deviceModels = queryDeviceModelsByItemIds(allItemIds);

        // 为每个网关创建映射关系并提交请求
        for (Map.Entry<String, List<String>> entry : gatewayItemIdMap.entrySet()) {
            String gatewayCode = entry.getKey();
            List<String> itemIds = entry.getValue();

            // 创建该网关的映射关系
            Map<String, String> gatewayMappings = createGatewayMappings(gatewayCode, itemIds, deviceModels);

            if (!gatewayMappings.isEmpty()) {
                // 提交异步请求
                CompletableFuture<Map<String, String>> future =
                    gatewayRequestManager.submitGatewayRequest(gatewayCode, itemIds, gatewayMappings);
                futures.add(future);

                log.info("已提交网关[{}]请求，设备数量: {}", gatewayCode, itemIds.size());
            } else {
                log.warn("网关[{}]没有找到有效的设备映射", gatewayCode);
            }
        }

        // 等待所有请求完成
        for (CompletableFuture<Map<String, String>> future : futures) {
            try {
                Map<String, String> gatewayResult = future.get(readTimeout + 1, TimeUnit.SECONDS);
                resultMap.putAll(gatewayResult);
            } catch (Exception e) {
                log.error("等待网关请求完成失败: {}", e.getMessage(), e);
            }
        }

        log.info("实时数据查询完成: 总共获取{}个数据点", resultMap.size());
        return resultMap;
    }

    /**
     * 为指定网关创建映射关系
     */
    private Map<String, String> createGatewayMappings(String gatewayCode, List<String> itemIds,
                                                      List<DeviceModelEntity> deviceModels) {
        Map<String, String> mappings = new HashMap<>();

        for (DeviceModelEntity model : deviceModels) {
            if (gatewayCode.equals(model.getGatewaycode()) && itemIds.contains(model.getItemid())) {
                String key = model.getGatewaycode() + "_" + model.getItemid();
                mappings.put(key, model.getDevProperty());
            }
        }

        log.debug("为网关[{}]创建了{}个映射关系", gatewayCode, mappings.size());
        return mappings;
    }

    /**
     * 使用网关锁机制处理实时数据查询（备用方案）
     */
    private Map<String, String> processRealTimeDataWithGatewayLock(Map<String, List<String>> gatewayItemIdMap) {
        Map<String, String> resultMap = new ConcurrentHashMap<>();

        // 为每个网关串行处理，避免并发冲突
        for (Map.Entry<String, List<String>> entry : gatewayItemIdMap.entrySet()) {
            String gatewayCode = entry.getKey();
            List<String> itemIds = entry.getValue();

            try {
                // 使用网关锁确保同一网关的请求串行执行
                Map<String, String> gatewayResult = gatewayLockService.executeWithGatewayLock(gatewayCode, () -> {
                    return querySingleGatewayData(gatewayCode, itemIds);
                });

                resultMap.putAll(gatewayResult);
                log.info("网关[{}]查询完成，获取{}个数据点", gatewayCode, gatewayResult.size());

            } catch (Exception e) {
                log.error("网关[{}]查询失败: {}", gatewayCode, e.getMessage(), e);
                // 单个网关失败不影响其他网关
            }
        }

        log.info("实时数据查询完成: 总共获取{}个数据点", resultMap.size());
        return resultMap;
    }

    /**
     * 查询单个网关的数据（在网关锁保护下执行）
     */
    private Map<String, String> querySingleGatewayData(String gatewayCode, List<String> itemIds) throws Exception {
        log.info("开始查询网关[{}]数据，设备数量: {}", gatewayCode, itemIds.size());

        // 构建MQTT命令
        JSONObject mqttCommand = new JSONObject();
        mqttCommand.put("cmd", ControlPathEnum.CMD_READ.code());

        JSONArray tags = new JSONArray();
        for (String itemId : itemIds) {
            tags.add(itemId);
        }
        mqttCommand.put("tagID", tags);

        // 发送MQTT请求
        String issueTopic = getIssueTopic(gatewayCode);
        DefaultFuture future = synMqttSender.sendReadMessage(issueTopic, mqttCommand);

        log.info("向网关[{}]发送MQTT读取请求，等待响应...", gatewayCode);

        // 等待响应
        Message message = future.get(readTimeout, TimeUnit.SECONDS);
        if (message == null) {
            log.warn("网关[{}]响应超时", gatewayCode);
            return new HashMap<>();
        }

        // 处理响应
        return processGatewayResponse(message, gatewayCode);
    }

    /**
     * 处理单个网关的响应
     */
    private Map<String, String> processGatewayResponse(Message message, String gatewayCode) {
        Map<String, String> resultMap = new HashMap<>();
        List<TDDataEntity> tdDataList = new ArrayList<>();

        try {
            String payload = message.getPlayLoad();
            JSONObject msgResult = JSONObject.parseObject(payload);
            String dataStr = msgResult.getString("data");

            if (dataStr == null) {
                log.warn("网关[{}]响应中缺少data字段", gatewayCode);
                return resultMap;
            }

            JSONArray dataArray = JSONArray.parseArray(dataStr);
            log.info("网关[{}]响应包含{}个设备数据项", gatewayCode, dataArray.size());

            // 处理每个设备数据
            for (int i = 0; i < dataArray.size(); i++) {
                JSONObject item = dataArray.getJSONObject(i);
                String mesDevId = item.getString("MESdevid");
                String dataValue = item.getString("DataValue");

                // 查找映射关系
                String mapKey = gatewayCode + "_" + mesDevId;
                String propCode = TaskUtil.iotReadPropMap.get(mapKey);

                if (StrUtil.isNotBlank(propCode)) {
                    resultMap.put(propCode, dataValue);

                    // 构建TDengine数据对象
                    TDDataEntity tdEngineData = TDDataEntity.builder()
                            .ts(DateUtils.getCurrentimeStr())
                            .gatewayCode(gatewayCode)
                            .itemid(mesDevId)
                            .devproperty(propCode)
                            .itemvalue(dataValue)
                            .build();

                    tdDataList.add(tdEngineData);

                    // 清理映射关系，避免影响后续请求
                    TaskUtil.iotReadPropMap.remove(mapKey);

                    log.debug("处理数据: gateway={}, device={}, prop={}, value={}",
                            gatewayCode, mesDevId, propCode, dataValue);
                } else {
                    log.debug("未找到映射: gateway={}, device={}", gatewayCode, mesDevId);
                }
            }

            // 异步存储到TDengine
            if (!tdDataList.isEmpty()) {
                try {
                    tdengineService.addToCacheQueue(tdDataList);
                    log.debug("网关[{}]已将{}条数据加入TDengine缓存队列", gatewayCode, tdDataList.size());
                } catch (Exception e) {
                    log.error("网关[{}]加入TDengine缓存队列失败: {}", gatewayCode, e.getMessage(), e);
                }
            }

        } catch (Exception e) {
            log.error("处理网关[{}]响应失败: {}", gatewayCode, e.getMessage(), e);
        }

        return resultMap;
    }

    /**
     * 根据itemId列表查询设备模型
     */
    private List<DeviceModelEntity> queryDeviceModelsByItemIds(List<String> itemIds) {
        if (itemIds == null || itemIds.isEmpty()) {
            return new ArrayList<>();
        }

        QueryWrapper<DeviceModelEntity> wrapper = new QueryWrapper<>();
        wrapper.in("itemid", itemIds);
        return deviceModelService.list(wrapper);
    }

    private Map<String, String> issueReadMqttMONI(HashMap<String, JSONObject> gatewayCommandMap) {
        HashMap<String, String> map = new HashMap<>();
        ArrayList<TDDataEntity> tdDataEntityList = new ArrayList<>();
        Random random = new Random();
        TaskUtil.iotReadPropMap.values().forEach(
                propCode -> {
                    map.put(propCode, String.format("%.2f", 100.0 * random.nextDouble()));
                    TDDataEntity tdDataEntity = TDDataEntity.builder()
                            .ts(DateUtils.getCurrentimeStr())
                            .devproperty(propCode)
                            .itemvalue(String.format("%.2f", 100.0 * random.nextDouble()))
                            .build();
                    tdDataEntityList.add(tdDataEntity);
                }
        );
        // 异步存储数据到TDengine
    /*    if (!tdDataEntityList.isEmpty()) {
            asyncTaskExecutor.execute(() -> {
                try {
                    tdengineService.insertTD(tdDataEntityList);
                } catch (Exception e) {
                    log.error("异步存储数据到TDengine失败: {}", e.getMessage(), e);
                    throw new ServerException(ErrorCode.INTERNAL_SERVER_ERROR);
                }
            });
        }*/
        TaskUtil.iotReadPropMap.clear();
        return map;
    }

    /**
     * 清理指定网关的映射关系，防止内存泄漏
     */
    private void cleanupGatewayMappings(String gatewaycode) {
        // 统计要清理的映射数量以便记录
        int count = 0;
        for (String key : TaskUtil.iotReadPropMap.keySet()) {
            if (key.startsWith(gatewaycode + "_")) {
                count++;
            }
        }

        if (count > 0) {
            // 删除所有以该网关开头的映射，避免内存泄漏
            TaskUtil.iotReadPropMap.entrySet().removeIf(entry ->
                    entry.getKey().startsWith(gatewaycode + "_"));

            log.info("已清理网关[{}]相关的{}个映射", gatewaycode, count);
        }
    }

    /**
     * 异步存储数据到TDengine（使用缓存队列）
     */
    private void asyncStoreToTDengine(List<TDDataEntity> tdDataEntityList) {
        if (tdDataEntityList == null || tdDataEntityList.isEmpty()) {
            return;
        }

        try {
            tdengineService.addToCacheQueue(tdDataEntityList);
            log.debug("已将{}条数据加入TDengine缓存队列", tdDataEntityList.size());
        } catch (Exception e) {
            log.error("加入TDengine缓存队列失败，数据量: {}, 错误: {}",
                    tdDataEntityList.size(), e.getMessage(), e);
            // 仅记录错误，不抛出异常，避免影响主流程
        }
    }
}

