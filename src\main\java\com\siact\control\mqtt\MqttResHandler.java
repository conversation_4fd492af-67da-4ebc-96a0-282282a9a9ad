package com.siact.control.mqtt;


import com.siact.control.utils.DefaultFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class MqttResHandler {


    public void deal(Message msg) {
        Long msgId = msg.getMessageId();
        String gateway = msg.getGatewway();

        // 优先使用网关ID和消息ID的组合查找
        if (DefaultFuture.contains(gateway, msgId)) {
            DefaultFuture.received(msg);
        } else if (DefaultFuture.contains(msgId)) {
            // 向后兼容
            DefaultFuture.received(msg);
        } else {
            log.warn("未找到匹配的Future: gateway={}, messageId={}", gateway, msgId);
        }
    }
}

