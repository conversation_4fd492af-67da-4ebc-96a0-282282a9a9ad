package com.siact.control.entity;/**
 * @Package com.siact.control.entity
 * @description:
 * <AUTHOR>
 * @create 2024/12/19 15:58
 */

import lombok.Builder;
import lombok.Data;

/**
 * @ClassName TDDataEntity
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/19 15:58
 * @Version 1.0
 **/
@Data
@Builder
public class TDDataEntity {
    private String ts;              // 时间戳
    private String gatewayCode;   // 网关编码
    private String itemid;      // 设备ID
    private String devproperty;  // 属性编码
    private String itemvalue;         // 属性值
}
