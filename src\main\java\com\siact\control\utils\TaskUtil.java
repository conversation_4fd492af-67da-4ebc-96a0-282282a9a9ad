package com.siact.control.utils;


import com.alibaba.fastjson.JSONObject;
import com.siact.control.entity.DeviceModelEntity;

import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Package com.siact.control.utils
 * @description: 全局变量
 * @create 2023/4/10 9:10
 */
public class TaskUtil {
    //存放网关+点位对应的短码对象
    public static ConcurrentHashMap<String, String> iotPropMap=new ConcurrentHashMap();
    public static ConcurrentHashMap<String, String> iotReadPropMap=new ConcurrentHashMap();
    //存放commandid对应的网关个数
    public static ConcurrentHashMap<Long, Integer> commandid2gatesizeMap=new ConcurrentHashMap();
    //存放网关和对应的websocket的url
    public static ConcurrentHashMap<String, String> gatewayWsMap=new ConcurrentHashMap();
    //存放充电桩的实体类(propcode,DeviceModelEntity)
    public static ConcurrentHashMap<String, DeviceModelEntity> chargeEntityMap=new ConcurrentHashMap();
    //存放非充电桩模型的实体类(devproperty,DeviceModelEntity)
    public static ConcurrentHashMap<String, DeviceModelEntity> entityMap=new ConcurrentHashMap();
    //存储充电桩控制中的桩号/枪号对应的dataCode
    public static ConcurrentHashMap<String,JSONObject> chargeDataCode=new ConcurrentHashMap<>();
}
