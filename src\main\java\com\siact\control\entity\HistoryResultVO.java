package com.siact.control.entity;/**
 * @Package com.siact.control.entity
 * @description:
 * <AUTHOR>
 * @create 2024/12/26 16:20
 */

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName HistoryResultVO
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/26 16:20
 * @Version 1.0
 **/
@Data
@ApiModel(value = "历史查询结果返回")
public class HistoryResultVO {
    @ApiModelProperty(value = "数据值列表")
    private List<DataCodeValDTO> dataCodeList;
    @ApiModelProperty(value = "时间")
    private String ts;
}
