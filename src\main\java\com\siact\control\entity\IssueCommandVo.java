package com.siact.control.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Package com.siact.control.entity
 * @description: 前端命令下发Vo类
 * @create 2023/4/3 13:50
 */
@ApiModel("控制指令详情")
@Data
public class IssueCommandVo{
    /**
     * 单点指令详情
     */
    @ApiModelProperty(value = "单点指令详情",position = 1)
    private List<DeviceControl> issueDetail;

    /**
     * 组合指令详情
     */
    @ApiModelProperty(
            value = "组合指令详情,每一个数组对应一个组合控制指令",
            position = 2,
            example = "[[{\"propCode\":\"prop1\",\"targetValue\":\"100\"}, {\"propCode\":\"prop2\",\"targetValue\":\"200\"}]]"
    )
    private List<List<DeviceControl>> multiIssueDetail;


    /**
     * 创建用户
     */
    @ApiModelProperty(value = "用户",position = 3)
    private String userAccount;

}
