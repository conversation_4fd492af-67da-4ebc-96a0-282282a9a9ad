package com.siact.control.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 网关锁服务
 * 确保同一个网关同时只能有一个实时数据查询请求
 * 
 * 解决方案核心思路：
 * 1. 为每个网关维护一个独立的锁
 * 2. 同一网关的请求必须串行执行
 * 3. 不同网关的请求可以并行执行
 * 4. 避免MQTT响应无法区分请求的问题
 */
@Service
@Slf4j
public class GatewayLockService {
    
    // 网关锁映射表
    private final ConcurrentHashMap<String, ReentrantLock> gatewayLocks = new ConcurrentHashMap<>();
    
    /**
     * 获取指定网关的锁
     * @param gatewayCode 网关编码
     * @return 网关对应的锁
     */
    public ReentrantLock getGatewayLock(String gatewayCode) {
        return gatewayLocks.computeIfAbsent(gatewayCode, k -> {
            log.debug("为网关[{}]创建新的锁", gatewayCode);
            return new ReentrantLock();
        });
    }
    
    /**
     * 执行网关级别的同步操作
     * @param gatewayCode 网关编码
     * @param operation 要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     */
    public <T> T executeWithGatewayLock(String gatewayCode, GatewayOperation<T> operation) {
        ReentrantLock lock = getGatewayLock(gatewayCode);
        lock.lock();
        try {
            log.debug("网关[{}]获得锁，开始执行操作", gatewayCode);
            return operation.execute();
        } finally {
            lock.unlock();
            log.debug("网关[{}]释放锁", gatewayCode);
        }
    }
    
    /**
     * 网关操作接口
     */
    @FunctionalInterface
    public interface GatewayOperation<T> {
        T execute() throws Exception;
    }
    
    /**
     * 清理不再使用的网关锁（可选的维护方法）
     */
    public void cleanupUnusedLocks() {
        // 这里可以实现清理逻辑，比如清理长时间未使用的锁
        // 为了简单起见，暂时不实现
        log.debug("当前维护{}个网关锁", gatewayLocks.size());
    }
}
