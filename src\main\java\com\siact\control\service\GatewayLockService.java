package com.siact.control.service;

import com.siact.control.config.GatewayRequestConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.*;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 网关锁服务
 * 确保同一个网关同时只能有一个实时数据查询请求
 * 
 * 解决方案核心思路：
 * 1. 为每个网关维护一个独立的锁
 * 2. 同一网关的请求必须串行执行
 * 3. 不同网关的请求可以并行执行
 * 4. 避免MQTT响应无法区分请求的问题
 */
@Service
@Slf4j
public class GatewayLockService {

    @Autowired
    private GatewayRequestConfig gatewayRequestConfig;

    // 网关锁映射表
    private final ConcurrentHashMap<String, ReentrantLock> gatewayLocks = new ConcurrentHashMap<>();
    
    /**
     * 获取指定网关的锁
     * @param gatewayCode 网关编码
     * @return 网关对应的锁
     */
    public ReentrantLock getGatewayLock(String gatewayCode) {
        return gatewayLocks.computeIfAbsent(gatewayCode, k -> {
            log.debug("为网关[{}]创建新的锁", gatewayCode);
            return new ReentrantLock();
        });
    }
    
    /**
     * 执行网关级别的同步操作（带超时控制）
     * @param gatewayCode 网关编码
     * @param operation 要执行的操作
     * @param timeoutSeconds 超时时间（秒）
     * @param <T> 返回值类型
     * @return 操作结果
     * @throws Exception 操作异常或超时异常
     */
    public <T> T executeWithGatewayLock(String gatewayCode, GatewayOperation<T> operation, long timeoutSeconds) throws Exception {
        ReentrantLock lock = getGatewayLock(gatewayCode);

        boolean acquired = false;
        try {
            // 尝试在指定时间内获取锁
            acquired = lock.tryLock(timeoutSeconds, TimeUnit.SECONDS);
            if (!acquired) {
                log.warn("网关[{}]获取锁超时，等待时间: {}秒", gatewayCode, timeoutSeconds);
                throw new RuntimeException("网关[" + gatewayCode + "]获取锁超时");
            }

            log.debug("网关[{}]获得锁，开始执行操作", gatewayCode);

            // 使用Future来控制操作执行的超时
            ExecutorService executor = Executors.newSingleThreadExecutor();
            try {
                Future<T> future = executor.submit(() -> {
                    try {
                        return operation.execute();
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                });

                // 等待操作完成，带超时控制
                return future.get(timeoutSeconds, TimeUnit.SECONDS);

            } catch (TimeoutException e) {
                log.error("网关[{}]操作执行超时，超时时间: {}秒", gatewayCode, timeoutSeconds);
                throw new RuntimeException("网关[" + gatewayCode + "]操作执行超时");
            } finally {
                executor.shutdownNow();
            }

        } catch (InterruptedException e) {
            log.error("网关[{}]操作被中断", gatewayCode);
            Thread.currentThread().interrupt();
            throw new RuntimeException("网关[" + gatewayCode + "]操作被中断");
        } finally {
            if (acquired) {
                lock.unlock();
                log.debug("网关[{}]释放锁", gatewayCode);
            }
        }
    }

    /**
     * 执行网关级别的同步操作（使用配置的默认超时时间）
     * @param gatewayCode 网关编码
     * @param operation 要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     */
    public <T> T executeWithGatewayLock(String gatewayCode, GatewayOperation<T> operation) {
        try {
            long timeoutSeconds = gatewayRequestConfig.getEffectiveLockTimeoutSeconds();
            return executeWithGatewayLock(gatewayCode, operation, timeoutSeconds);
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            }
            throw new RuntimeException(e);
        }
    }
    
    /**
     * 网关操作接口
     */
    @FunctionalInterface
    public interface GatewayOperation<T> {
        T execute() throws Exception;
    }
    
    /**
     * 清理不再使用的网关锁（可选的维护方法）
     */
    public void cleanupUnusedLocks() {
        // 这里可以实现清理逻辑，比如清理长时间未使用的锁
        // 为了简单起见，暂时不实现
        log.debug("当前维护{}个网关锁", gatewayLocks.size());
    }
}
