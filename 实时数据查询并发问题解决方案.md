# 实时数据查询并发问题解决方案

## 问题描述

### 原始问题
当同一个网关在同一时间有多次实时数据查询请求时，出现以下问题：
1. **固定ID冲突**：所有实时数据查询都使用固定的messageId `99999L`
2. **Future覆盖**：后发送的请求会覆盖前面的Future，导致前面的请求无法收到响应
3. **映射混乱**：`TaskUtil.iotReadPropMap`中的映射关系会被多次请求互相干扰

### 问题根因分析
```java
// 原始代码问题1：固定ID
long messageId = 99999L;  // 所有请求都使用相同ID

// 原始代码问题2：全局映射覆盖
TaskUtil.iotReadPropMap.put(deviceModel.getGatewaycode() + "_" + deviceModel.getItemid(), 
                           deviceModel.getDevProperty());
```

## 解决方案

### 核心思路
1. **唯一请求ID**：为每个请求生成唯一的ID，避免ID冲突
2. **请求级映射**：每个请求使用独立的映射表，避免并发干扰
3. **专用服务类**：创建`RealTimeDataService`专门处理实时数据查询

### 方案架构

#### 1. 唯一ID生成机制
```java
// 使用时间戳+递增序列确保唯一性
private final AtomicLong requestIdGenerator = new AtomicLong(System.currentTimeMillis());

private long generateUniqueReadId() {
    return requestIdGenerator.incrementAndGet();
}
```

#### 2. 请求级映射管理
```java
// 存储每个请求的映射关系 <requestId, <gatewayCode_itemId, propCode>>
private final Map<Long, Map<String, String>> requestMappings = new ConcurrentHashMap<>();

// 为每个请求创建独立映射
Map<String, String> mappings = createRequestMappings(deviceModels);
requestMappings.put(requestId, mappings);
```

#### 3. MQTT消息增强
```java
// 在MQTT消息中添加requestId
mqttCommand.put("requestId", requestId);

// 响应处理时使用requestId匹配
Long requestId = msgObj.getLong("requestId");
Map<String, String> mappings = requestMappings.get(requestId);
```

## 核心代码实现

### 1. RealTimeDataService（新增）
专门处理实时数据查询的服务类：

**主要功能：**
- 生成唯一请求ID
- 管理请求级映射关系
- 发送MQTT请求并等待响应
- 处理响应数据并清理资源

**关键方法：**
```java
public Map<String, String> queryRealTimeData(Map<String, List<String>> gatewayItemIdMap, 
                                              List<DeviceModelEntity> deviceModels)
```

### 2. SynMqttSender（优化）
MQTT发送器的优化：

**主要改进：**
- 支持动态requestId
- 向后兼容原有逻辑
- 增强日志记录

**关键改进：**
```java
// 检查消息中是否已有requestId，如果没有则生成一个
Long requestId = msg.getLong("requestId");
if (requestId == null) {
    requestId = generateUniqueReadId();
    msg.put("requestId", requestId);
}
```

### 3. MqttMessageHandle（优化）
MQTT消息处理器的优化：

**主要改进：**
- 支持从响应中提取requestId
- 向后兼容旧版本（默认99999L）
- 增强错误处理

**关键改进：**
```java
// 尝试从响应中获取requestId，如果没有则使用默认值99999L（向后兼容）
Long requestId = msgObj.getLong("requestId");
if (requestId == null) {
    requestId = 99999L; // 向后兼容旧版本
}
```

### 4. StrategyService（重构）
策略服务的重构：

**主要改进：**
- 使用新的RealTimeDataService
- 保持测试模式兼容
- 简化代码逻辑

## 解决效果

### 1. 并发安全性
- ✅ 每个请求使用唯一ID，避免ID冲突
- ✅ 请求级映射管理，避免并发干扰
- ✅ 线程安全的数据结构

### 2. 性能优化
- ✅ 减少全局锁竞争
- ✅ 请求完成后自动清理资源
- ✅ 异步TDengine存储不阻塞主流程

### 3. 向后兼容
- ✅ 保持原有API接口不变
- ✅ 支持旧版本MQTT响应格式
- ✅ 测试模式正常工作

### 4. 可维护性
- ✅ 代码结构更清晰
- ✅ 职责分离更明确
- ✅ 日志记录更完善

## 使用方式

### 正常调用（无需修改）
```java
// 原有调用方式保持不变
Map<String, String> result = strategyService.issueRealTimeDataMessage(gatewayItemIdMap);
```

### 直接使用新服务
```java
// 也可以直接使用新服务
Map<String, String> result = realTimeDataService.queryRealTimeData(gatewayItemIdMap, deviceModels);
```

## 测试验证

### 1. 并发测试
- 多线程同时查询不同网关
- 多线程同时查询同一网关
- 高频并发请求测试

### 2. 兼容性测试
- 旧版本MQTT响应兼容性
- 测试模式功能验证
- API接口向后兼容

### 3. 性能测试
- 并发性能对比
- 内存使用情况
- 响应时间统计

## 监控建议

### 1. 关键指标
- 并发请求数量
- 请求响应时间
- 映射表大小
- 异常请求比例

### 2. 日志监控
- 请求ID生成情况
- 映射关系创建和清理
- MQTT响应匹配成功率
- 异常情况记录

## 注意事项

1. **内存管理**：请求完成后会自动清理映射关系，避免内存泄漏
2. **超时处理**：请求超时后Future会自动清理，不会影响后续请求
3. **异常恢复**：单个请求失败不会影响其他并发请求
4. **向后兼容**：保持与现有系统的完全兼容

这个解决方案彻底解决了实时数据查询的并发ID冲突问题，同时保持了系统的稳定性和向后兼容性。
