package com.siact.control.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.siact.control.entity.DeviceModelEntity;
import com.siact.control.entity.TDDataEntity;
import com.siact.control.enume.ControlPathEnum;
import com.siact.control.mqtt.DefaultFuture;
import com.siact.control.mqtt.Message;
import com.siact.control.mqtt.SynMqttSender;
import com.siact.control.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 实时数据查询服务
 * 解决并发查询时ID冲突的问题
 */
@Service
@Slf4j
public class RealTimeDataService {

    @Autowired
    private SynMqttSender synMqttSender;
    
    @Autowired
    private TDengineService tdengineService;
    
    @Value("${mqtt.prefixTopic}")
    private String prefixTopic;
    
    @Value("${mqtt.suffixTopic}")
    private String suffixTopic;
    
    @Value("${mqtt.readTimeout}")
    private int readTimeout;
    
    // 请求ID生成器
    private final AtomicLong requestIdGenerator = new AtomicLong(System.currentTimeMillis());
    
    // 存储每个请求的映射关系 <requestId, <gatewayCode_itemId, propCode>>
    private final Map<Long, Map<String, String>> requestMappings = new ConcurrentHashMap<>();

    /**
     * 发送实时数据查询请求
     * @param gatewayItemIdMap 网关到设备ID的映射
     * @param deviceModels 设备模型列表
     * @return 查询结果
     */
    public Map<String, String> queryRealTimeData(Map<String, List<String>> gatewayItemIdMap, 
                                                  List<DeviceModelEntity> deviceModels) {
        if (gatewayItemIdMap == null || gatewayItemIdMap.isEmpty()) {
            log.warn("没有网关项目ID映射数据，无法发送MQTT消息");
            return new HashMap<>();
        }

        log.info("开始实时数据查询: 共{}个网关", gatewayItemIdMap.size());

        // 为本次请求生成唯一ID
        long requestId = requestIdGenerator.incrementAndGet();
        
        // 创建本次请求的映射关系
        Map<String, String> mappings = createRequestMappings(deviceModels);
        requestMappings.put(requestId, mappings);
        
        try {
            // 发送MQTT请求并等待响应
            return sendMqttAndWaitResponse(gatewayItemIdMap, requestId);
        } finally {
            // 清理本次请求的映射关系
            requestMappings.remove(requestId);
        }
    }

    /**
     * 创建本次请求的映射关系
     */
    private Map<String, String> createRequestMappings(List<DeviceModelEntity> deviceModels) {
        Map<String, String> mappings = new ConcurrentHashMap<>();
        for (DeviceModelEntity model : deviceModels) {
            String key = model.getGatewaycode() + "_" + model.getItemid();
            mappings.put(key, model.getDevProperty());
        }
        log.debug("创建了{}个映射关系", mappings.size());
        return mappings;
    }

    /**
     * 发送MQTT请求并等待响应
     */
    private Map<String, String> sendMqttAndWaitResponse(Map<String, List<String>> gatewayItemIdMap, long requestId) {
        Map<String, String> resultMap = new ConcurrentHashMap<>();
        List<DefaultFuture> futures = new ArrayList<>();

        // 发送MQTT消息到各网关
        for (Map.Entry<String, List<String>> entry : gatewayItemIdMap.entrySet()) {
            String gatewaycode = entry.getKey();
            List<String> itemIds = entry.getValue();
            
            try {
                // 构建MQTT命令
                JSONObject mqttCommand = new JSONObject();
                mqttCommand.put("cmd", ControlPathEnum.CMD_READ.code());
                mqttCommand.put("requestId", requestId); // 添加请求ID
                
                JSONArray tags = new JSONArray();
                for (String itemId : itemIds) {
                    tags.add(itemId);
                }
                mqttCommand.put("tagID", tags);

                // 发送MQTT消息
                String issueTopic = getIssueTopic(gatewaycode);
                DefaultFuture future = synMqttSender.sendReadMessage(issueTopic, mqttCommand);
                futures.add(future);
                
                log.info("向网关[{}]发送MQTT读取请求: requestId={}, itemIds={}", 
                        gatewaycode, requestId, itemIds.size());
                        
            } catch (Exception e) {
                log.error("向网关[{}]发送MQTT请求失败: {}", gatewaycode, e.getMessage(), e);
            }
        }

        // 等待所有响应
        List<TDDataEntity> tdDataList = new ArrayList<>();
        for (DefaultFuture future : futures) {
            try {
                Message message = future.get(readTimeout, TimeUnit.SECONDS);
                if (message != null) {
                    processResponse(message, requestId, resultMap, tdDataList);
                } else {
                    log.warn("等待MQTT响应超时: requestId={}", requestId);
                }
            } catch (Exception e) {
                log.error("处理MQTT响应失败: requestId={}, error={}", requestId, e.getMessage(), e);
            }
        }

        // 异步存储到TDengine
        if (!tdDataList.isEmpty()) {
            try {
                tdengineService.addToCacheQueue(tdDataList);
                log.debug("已将{}条数据加入TDengine缓存队列", tdDataList.size());
            } catch (Exception e) {
                log.error("加入TDengine缓存队列失败: {}", e.getMessage(), e);
            }
        }

        log.info("实时数据查询完成: requestId={}, 获取{}个数据点", requestId, resultMap.size());
        return resultMap;
    }

    /**
     * 处理MQTT响应
     */
    private void processResponse(Message message, long requestId, Map<String, String> resultMap, 
                                List<TDDataEntity> tdDataList) {
        try {
            String payload = message.getPlayLoad();
            String gatewaycode = message.getGatewway();
            
            JSONObject msgResult = JSONObject.parseObject(payload);
            String dataStr = msgResult.getString("data");
            JSONArray dataArray = JSONArray.parseArray(dataStr);
            
            // 获取本次请求的映射关系
            Map<String, String> mappings = requestMappings.get(requestId);
            if (mappings == null) {
                log.warn("找不到请求映射: requestId={}", requestId);
                return;
            }

            // 处理响应数据
            for (int i = 0; i < dataArray.size(); i++) {
                JSONObject item = dataArray.getJSONObject(i);
                String mesDevId = item.getString("MESdevid");
                String dataValue = item.getString("DataValue");

                String mapKey = gatewaycode + "_" + mesDevId;
                String propCode = mappings.get(mapKey);

                if (StrUtil.isNotBlank(propCode)) {
                    resultMap.put(propCode, dataValue);

                    // 构建TDengine数据对象
                    TDDataEntity tdEngineData = TDDataEntity.builder()
                            .ts(DateUtils.getCurrentimeStr())
                            .gatewayCode(gatewaycode)
                            .itemid(mesDevId)
                            .devproperty(propCode)
                            .itemvalue(dataValue)
                            .build();

                    tdDataList.add(tdEngineData);
                    
                    log.debug("处理数据: gateway={}, device={}, prop={}, value={}", 
                            gatewaycode, mesDevId, propCode, dataValue);
                } else {
                    log.debug("未找到映射: gateway={}, device={}", gatewaycode, mesDevId);
                }
            }
            
        } catch (Exception e) {
            log.error("处理MQTT响应数据失败: requestId={}, error={}", requestId, e.getMessage(), e);
        }
    }

    /**
     * 构建发送主题
     */
    private String getIssueTopic(String gatewaycode) {
        return prefixTopic + gatewaycode + suffixTopic;
    }
}
