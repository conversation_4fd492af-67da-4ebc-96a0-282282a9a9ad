package com.siact.control.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.siact.control.config.GatewayRequestConfig;
import com.siact.control.entity.TDDataEntity;
import com.siact.control.enume.ControlPathEnum;
import com.siact.control.utils.DefaultFuture;
import com.siact.control.mqtt.Message;
import com.siact.control.mqtt.SynMqttSender;
import com.siact.control.utils.DateUtils;
import com.siact.control.utils.TaskUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;

/**
 * 网关请求管理器
 * 在发送阶段控制并发，避免同一网关的请求冲突
 * 
 * 核心思路：
 * 1. 维护每个网关的请求队列
 * 2. 同一网关同时只能有一个活跃请求
 * 3. 请求完成后自动处理队列中的下一个请求
 * 4. 不同网关的请求可以并行处理
 */
@Service
@Slf4j
public class GatewayRequestManager {

    @Autowired
    private SynMqttSender synMqttSender;

    @Autowired
    private TDengineService tdengineService;

    @Autowired
    private GatewayRequestConfig gatewayRequestConfig;

    @Value("${mqtt.prefixTopic}")
    private String prefixTopic;

    @Value("${mqtt.suffixTopic}")
    private String suffixTopic;

    @Value("${mqtt.readTimeout}")
    private int readTimeout;
    
    // 网关请求队列管理
    private final Map<String, GatewayRequestQueue> gatewayQueues = new ConcurrentHashMap<>();
    
    // 线程池用于异步处理请求
    private final ExecutorService requestExecutor = Executors.newCachedThreadPool(
            r -> new Thread(r, "GatewayRequest-" + System.currentTimeMillis()));

    /**
     * 网关请求队列
     */
    @Data
    private static class GatewayRequestQueue {
        private final String gatewayCode;
        private final Queue<PendingRequest> pendingRequests = new ConcurrentLinkedQueue<>();
        private volatile boolean processing = false;
        
        public GatewayRequestQueue(String gatewayCode) {
            this.gatewayCode = gatewayCode;
        }
    }
    
    /**
     * 待处理请求
     */
    @Data
    private static class PendingRequest {
        private final String gatewayCode;
        private final List<String> itemIds;
        private final Map<String, String> mappings;
        private final CompletableFuture<Map<String, String>> resultFuture;
        private final String requestId;
        
        public PendingRequest(String gatewayCode, List<String> itemIds, 
                            Map<String, String> mappings, String requestId) {
            this.gatewayCode = gatewayCode;
            this.itemIds = itemIds;
            this.mappings = mappings;
            this.requestId = requestId;
            this.resultFuture = new CompletableFuture<>();
        }
    }

    /**
     * 提交网关请求（异步）
     * @param gatewayCode 网关编码
     * @param itemIds 设备ID列表
     * @param mappings 映射关系
     * @return 异步结果Future
     */
    public CompletableFuture<Map<String, String>> submitGatewayRequest(String gatewayCode, 
                                                                       List<String> itemIds, 
                                                                       Map<String, String> mappings) {
        String requestId = gatewayCode + "_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
        PendingRequest request = new PendingRequest(gatewayCode, itemIds, mappings, requestId);
        
        log.info("提交网关[{}]请求: requestId={}, itemIds={}", gatewayCode, requestId, itemIds.size());
        
        // 获取或创建网关队列
        GatewayRequestQueue queue = gatewayQueues.computeIfAbsent(gatewayCode, GatewayRequestQueue::new);
        
        // 将请求加入队列
        queue.getPendingRequests().offer(request);
        
        // 尝试处理队列（如果当前没有在处理）
        tryProcessQueue(queue);
        
        return request.getResultFuture();
    }
    
    /**
     * 尝试处理网关队列
     */
    private void tryProcessQueue(GatewayRequestQueue queue) {
        synchronized (queue) {
            if (queue.isProcessing()) {
                log.debug("网关[{}]正在处理请求，新请求已加入队列", queue.getGatewayCode());
                return;
            }
            
            PendingRequest nextRequest = queue.getPendingRequests().poll();
            if (nextRequest == null) {
                log.debug("网关[{}]队列为空", queue.getGatewayCode());
                return;
            }
            
            // 标记为正在处理
            queue.setProcessing(true);
            
            // 异步处理请求
            requestExecutor.submit(() -> processRequest(queue, nextRequest));
        }
    }
    
    /**
     * 处理单个请求
     */
    private void processRequest(GatewayRequestQueue queue, PendingRequest request) {
        try {
            log.info("开始处理网关[{}]请求: requestId={}", request.getGatewayCode(), request.getRequestId());
            
            // 设置临时映射关系
            setTemporaryMappings(request.getMappings());
            
            // 发送MQTT请求
            Map<String, String> result = sendMqttRequest(request);
            
            // 完成请求
            request.getResultFuture().complete(result);
            
            log.info("网关[{}]请求处理完成: requestId={}, 结果数量={}", 
                    request.getGatewayCode(), request.getRequestId(), result.size());
            
        } catch (Exception e) {
            log.error("网关[{}]请求处理失败: requestId={}, error={}", 
                    request.getGatewayCode(), request.getRequestId(), e.getMessage(), e);
            request.getResultFuture().completeExceptionally(e);
        } finally {
            // 清理临时映射关系
            clearTemporaryMappings(request.getMappings());
            
            // 标记处理完成
            synchronized (queue) {
                queue.setProcessing(false);
            }
            
            // 处理队列中的下一个请求
            tryProcessQueue(queue);
        }
    }
    
    /**
     * 发送MQTT请求并等待响应
     */
    private Map<String, String> sendMqttRequest(PendingRequest request) throws Exception {
        // 构建MQTT命令
        JSONObject mqttCommand = new JSONObject();
        mqttCommand.put("cmd", ControlPathEnum.CMD_READ.code());
        
        JSONArray tags = new JSONArray();
        for (String itemId : request.getItemIds()) {
            tags.add(itemId);
        }
        mqttCommand.put("tagID", tags);
        
        // 发送MQTT请求
        String issueTopic = getIssueTopic(request.getGatewayCode());
        DefaultFuture future = synMqttSender.sendReadMessage(issueTopic, mqttCommand);
        
        log.info("向网关[{}]发送MQTT请求: requestId={}", request.getGatewayCode(), request.getRequestId());
        
        // 等待响应，使用配置的超时时间
        int timeoutSeconds = gatewayRequestConfig.getEffectiveTimeoutSeconds();
        Message message = null;
        try {
            message = future.get(timeoutSeconds, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("网关[{}]MQTT响应超时: requestId={}, 超时时间: {}秒",
                    request.getGatewayCode(), request.getRequestId(), timeoutSeconds);
            throw new RuntimeException("网关[" + request.getGatewayCode() + "]MQTT响应超时");
        }

        if (message == null) {
            log.warn("网关[{}]响应为空: requestId={}", request.getGatewayCode(), request.getRequestId());
            return new HashMap<>();
        }
        
        // 处理响应
        return processResponse(message, request);
    }
    
    /**
     * 处理MQTT响应
     */
    private Map<String, String> processResponse(Message message, PendingRequest request) {
        Map<String, String> resultMap = new HashMap<>();
        List<TDDataEntity> tdDataList = new ArrayList<>();
        
        try {
            String payload = message.getPlayLoad();
            JSONObject msgResult = JSONObject.parseObject(payload);
            String dataStr = msgResult.getString("data");
            
            if (dataStr == null) {
                log.warn("网关[{}]响应中缺少data字段: requestId={}", request.getGatewayCode(), request.getRequestId());
                return resultMap;
            }
            
            JSONArray dataArray = JSONArray.parseArray(dataStr);
            log.info("网关[{}]响应包含{}个设备数据项: requestId={}", 
                    request.getGatewayCode(), dataArray.size(), request.getRequestId());
            
            // 处理每个设备数据
            for (int i = 0; i < dataArray.size(); i++) {
                JSONObject item = dataArray.getJSONObject(i);
                String mesDevId = item.getString("MESdevid");
                String dataValue = item.getString("DataValue");
                
                // 使用请求的映射关系
                String mapKey = request.getGatewayCode() + "_" + mesDevId;
                String propCode = request.getMappings().get(mapKey);
                
                if (StrUtil.isNotBlank(propCode)) {
                    resultMap.put(propCode, dataValue);
                    
                    // 构建TDengine数据对象
                    TDDataEntity tdEngineData = TDDataEntity.builder()
                            .ts(DateUtils.getCurrentimeStr())
                            .gatewayCode(request.getGatewayCode())
                            .itemid(mesDevId)
                            .devproperty(propCode)
                            .itemvalue(dataValue)
                            .build();
                    
                    tdDataList.add(tdEngineData);
                    
                    log.debug("处理数据: gateway={}, device={}, prop={}, value={}", 
                            request.getGatewayCode(), mesDevId, propCode, dataValue);
                } else {
                    log.debug("未找到映射: gateway={}, device={}", request.getGatewayCode(), mesDevId);
                }
            }
            
            // 异步存储到TDengine
            if (!tdDataList.isEmpty()) {
                try {
                    tdengineService.addToCacheQueue(tdDataList);
                    log.debug("网关[{}]已将{}条数据加入TDengine缓存队列: requestId={}", 
                            request.getGatewayCode(), tdDataList.size(), request.getRequestId());
                } catch (Exception e) {
                    log.error("网关[{}]加入TDengine缓存队列失败: requestId={}, error={}", 
                            request.getGatewayCode(), request.getRequestId(), e.getMessage(), e);
                }
            }
            
        } catch (Exception e) {
            log.error("处理网关[{}]响应失败: requestId={}, error={}", 
                    request.getGatewayCode(), request.getRequestId(), e.getMessage(), e);
        }
        
        return resultMap;
    }
    
    /**
     * 设置临时映射关系
     */
    private void setTemporaryMappings(Map<String, String> mappings) {
        for (Map.Entry<String, String> entry : mappings.entrySet()) {
            TaskUtil.iotReadPropMap.put(entry.getKey(), entry.getValue());
        }
    }
    
    /**
     * 清理临时映射关系
     */
    private void clearTemporaryMappings(Map<String, String> mappings) {
        for (String key : mappings.keySet()) {
            TaskUtil.iotReadPropMap.remove(key);
        }
    }
    
    /**
     * 构建发送主题
     */
    private String getIssueTopic(String gatewayCode) {
        return prefixTopic + gatewayCode + suffixTopic;
    }
    
    /**
     * 获取网关队列状态（用于监控）
     */
    public Map<String, Integer> getGatewayQueueStatus() {
        Map<String, Integer> status = new HashMap<>();
        for (Map.Entry<String, GatewayRequestQueue> entry : gatewayQueues.entrySet()) {
            status.put(entry.getKey(), entry.getValue().getPendingRequests().size());
        }
        return status;
    }
}
