package com.siact.control.utils;

import com.alibaba.druid.pool.DruidDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * @program: shandong_energyefficiency_project
 * @description:
 * @author: <PERSON>
 * @create: 2021-07-12 15:50
 **/
@Configuration
public class DruidTdDataUtil {

    private static DruidDataSource dataSource = new DruidDataSource();

    @Autowired
    TDengineProperties tDengineProperties;

    @PostConstruct
    public void initialDataSource(){
        try {
            // jdbc properties
//            dataSource.setDriverClassName(ConfigUtil.configProperties.getProperty("tdDriverClassName"));
//            dataSource.setUrl(ConfigUtil.configProperties.getProperty("tdDataUrl"));
//            dataSource.setUsername(ConfigUtil.configProperties.getProperty("tdUsername"));
//            dataSource.setPassword(ConfigUtil.configProperties.getProperty("tdPassword"));
//            // pool configurations
//            dataSource.setInitialSize(Integer.valueOf(ConfigUtil.configProperties.getProperty("td_initialSize")));
//            dataSource.setMinIdle(Integer.valueOf(ConfigUtil.configProperties.getProperty("td_minIdle")));
//            dataSource.setMaxActive(Integer.valueOf(ConfigUtil.configProperties.getProperty("td_maxActive")));
//            dataSource.setMaxWait(Integer.valueOf(ConfigUtil.configProperties.getProperty("td_maxWait")));
//            dataSource.setValidationQuery("select server_status()");
//            dataSource.setDefaultAutoCommit(Boolean.valueOf(ConfigUtil.configProperties.getProperty("tdDefaultAutoCommit")));
            dataSource.setDriverClassName(tDengineProperties.getDriverClassName());
            dataSource.setUrl(tDengineProperties.getUrl());
            dataSource.setUsername(tDengineProperties.getUsername());
            dataSource.setPassword(tDengineProperties.getPassword());
            // pool configurations
            dataSource.setInitialSize(tDengineProperties.getInitialSize());
            dataSource.setMinIdle(tDengineProperties.getMinIdle());
            dataSource.setMaxActive(tDengineProperties.getMaxActive());
            dataSource.setMaxWait(tDengineProperties.getMaxWait());
            dataSource.setValidationQuery("select server_status()");
            dataSource.setDefaultAutoCommit(tDengineProperties.getDefaultAutoCommit());


            //超过时间限制是否回收
            //dataSource.setRemoveAbandoned(true);
            //超时时间；单位为秒
            //dataSource.setRemoveAbandonedTimeout(60);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
   /* static {

    }*/

    /**
     * 获取连接
     */
    public static Connection getConnection() throws SQLException {
        return dataSource.getConnection();
    }

    /**
     * 释放资源
     */
    public static void close(Connection conn, Statement stat, ResultSet rs) {
        try {
            if (rs != null) {
                rs.close();
            }
            if (stat != null) {
                stat.close();
            }
            if (conn != null) {
                conn.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }


    public static DruidDataSource getDataSource() {
        return dataSource;
    }
}
