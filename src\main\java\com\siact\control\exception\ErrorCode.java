package com.siact.control.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 错误编码
 *
*/
@Getter
@AllArgsConstructor
public enum ErrorCode {

    REQUEST_TIMEOUT(504,"接口请求超时"),
    INTERNAL_SERVER_ERROR(500, "服务器异常，请稍后再试"),
    MODEL_NOT_MATCH(501,"模型不匹配"),
    COMM_EXCEPTION(10000,"网络通信异常"),
    PARAM_ERROR(10001,"参数校验失败");

    private final int code;
    private final String msg;
}
