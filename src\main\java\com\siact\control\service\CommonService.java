package com.siact.control.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.api.R;
import com.siact.control.entity.*;
import com.siact.control.exception.ErrorCode;
import com.siact.control.exception.ServerException;
import com.siact.control.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Package com.siact.control.service
 * @description:
 * @create 2023/5/8 17:59
 */
@Component
@Slf4j
public class CommonService {

    @Autowired
    CommandDetailService commandDetailService;
    @Autowired
    DeviceModelService deviceModelService;
    @Autowired
    StrategyService strategyService;
    @Autowired
    TDengineService tdengineService;

    public Long saveCommand(IssueCommandVo issueCommandVo) {

        //1.将解密后的命令保存
        CommandDetailEntity commandDetailEntity = new CommandDetailEntity();
        commandDetailEntity.setIssueDetail(JSON.toJSONString(issueCommandVo.getIssueDetail()));
        commandDetailEntity.setMultiIssueDetail(JSON.toJSONString(issueCommandVo.getMultiIssueDetail()));
        commandDetailEntity.setCreateBy(issueCommandVo.getUserAccount());
        commandDetailEntity.setEndFlag(1);
        Long commandId = commandDetailService.insert(commandDetailEntity);
        log.info("控制命令入库成功======"+commandId);
        return commandId;
    }


    /**
     * @return
     * <AUTHOR>
     * @Description // 任务执行
     * @Date 13:52 2023/8/17
     * @Param
     **/
    public Result execute(IssueCommandVo issueCommandVo, Long commandId) {
        List<DeviceControl> issueDetail = issueCommandVo.getIssueDetail();
        List<List<DeviceControl>> multiIssueDetail = issueCommandVo.getMultiIssueDetail();
        //读取mysql模型数据到内存中
        deviceModelService.getAllPropCodeAndIotType();
        //1.将JSONArray commandDetail转化为 ===》<devCode + "α" + propCode,value> ===》  为<gateway,<point,value>>
        Map<String, Map<String, List<DeviceControl>>> deviceControlMap = strategyService.getDeviceControlMap(issueDetail,multiIssueDetail);
        //2.将命令组装称为MQTT需要的格式下发
        Result mapResult = strategyService.issueMessage(commandId, deviceControlMap,issueCommandVo);
        return mapResult.getCode() == 0 ? Result.ok(mapResult.getData()) : Result.error(mapResult.getCode(), mapResult.getMsg());
    }

    public Map<String,String> getRealTimeData(List<String> dataCodeList) {
        //将请求的dataCodeList转化为<gatewaycode,List<itemid>>
        Map<String, List<String>> gatewayItemIdMap = strategyService.getGatewayItemIdMap(dataCodeList);
        //封装下发mqtt的指令
        return strategyService.issueRealTimeDataMessage(gatewayItemIdMap);
    }

    public HistoryResultVO getHistoryDataVal(HistoryDataVO historyDataVO) {
        // 1. 参数校验
        if (historyDataVO == null) {
            throw new ServerException(ErrorCode.PARAM_ERROR);
        }

        List<String> dataCodeList = historyDataVO.getDataCodeList();
        String timestamp = historyDataVO.getTs();

        // 校验数据编码列表
        if (ObjectUtil.isEmpty(dataCodeList)) {
            throw new ServerException(ErrorCode.PARAM_ERROR);
        }

        // 校验时间戳
        if (StrUtil.isBlank(timestamp)) {
            throw new ServerException(ErrorCode.PARAM_ERROR);
        }

        try {
            // 2. 构建返回对象
            HistoryResultVO resultVO = new HistoryResultVO();
            resultVO.setTs(timestamp);

            // 3. 查询TDengine数据库
            List<TDDataEntity> tdDataList = tdengineService.queryHistoryData(dataCodeList, timestamp);
            
            // 4. 转换查询结果
            if (!ObjectUtil.isEmpty(tdDataList)) {
                List<DataCodeValDTO> dataCodeValList = tdDataList.stream()
                    .map(tdData -> {
                        DataCodeValDTO dto = new DataCodeValDTO();
                        dto.setDataCode(tdData.getDevproperty());  // 设置属性编码
                        dto.setVal(tdData.getItemvalue());   // 设置属性值
                        return dto;
                    })
                    .collect(Collectors.toList());
                
                resultVO.setDataCodeList(dataCodeValList);
            } else {
                // 如果没有查询到数据，返回空值列表
                List<DataCodeValDTO> emptyList = dataCodeList.stream()
                    .map(dataCode -> {
                        DataCodeValDTO dto = new DataCodeValDTO();
                        dto.setDataCode(dataCode);
                        dto.setVal(null);  // 或者设置为默认值，比如"0"
                        return dto;
                    })
                    .collect(Collectors.toList());
                
                resultVO.setDataCodeList(emptyList);
            }

            return resultVO;

        } catch (Exception e) {
            log.error("查询历史数据失败: {}", e.getMessage(), e);
            throw new ServerException(ErrorCode.INTERNAL_SERVER_ERROR);
        }
    }
}