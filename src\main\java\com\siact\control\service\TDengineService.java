package com.siact.control.service;/**
 * @Package com.siact.control.service
 * @description:
 * <AUTHOR>
 * @create 2024/12/19 16:18
 */

import cn.hutool.core.util.ObjectUtil;
import com.siact.control.entity.TDDataEntity;
import com.siact.control.exception.ErrorCode;
import com.siact.control.exception.ServerException;
import com.siact.control.utils.DruidTdDataUtil;
import com.siact.control.utils.TDengineProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

/**
 * @ClassName TDengineService
 * @Description TDengine数据库服务类，优化了并发插入性能
 * <AUTHOR>
 * @Date 2024/12/19 16:18
 * @Version 1.0
 **/
@Service
@Slf4j
public class TDengineService {

    @Autowired
    TDengineProperties tDengineProperties;

    // 数据缓存队列（延迟初始化）
    private BlockingQueue<TDDataEntity> dataQueue;

    // 定时任务执行器
    private final ScheduledExecutorService scheduler = new ScheduledThreadPoolExecutor(1,
            r -> new Thread(r, "TDengine-BatchInsert"));

    // 批量插入锁
    private final ReentrantLock batchLock = new ReentrantLock();

    /**
     * 初始化定时批量插入任务
     */
    @PostConstruct
    public void initBatchInsertTask() {
        // 初始化数据队列
        dataQueue = new LinkedBlockingQueue<>(tDengineProperties.getBatch().getCacheQueueSize());

        // 启动定时批量插入任务 - 5秒插入一次
        long flushInterval = tDengineProperties.getBatch().getFlushIntervalMs();
        scheduler.scheduleWithFixedDelay(this::flushCachedData,
                flushInterval, flushInterval, TimeUnit.MILLISECONDS);
        log.info("TDengine批量插入任务已启动，刷新间隔: {}ms, 队列大小: {}",
                flushInterval, tDengineProperties.getBatch().getCacheQueueSize());
    }

    /**
     * 销毁时关闭定时任务
     */
    @PreDestroy
    public void destroy() {
        // 先刷新剩余数据
        flushCachedData();

        // 关闭定时任务
        scheduler.shutdown();
        try {
            long maxWaitTime = tDengineProperties.getBatch().getMaxWaitTimeMs();
            if (!scheduler.awaitTermination(maxWaitTime, TimeUnit.MILLISECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        log.info("TDengine批量插入任务已关闭");
    }

    /**
     * 高频实时数据缓存插入方法
     * 将数据加入缓存队列，由定时任务每5秒批量插入
     * @param tdDataList 待插入的数据列表
     */
    public void addToCacheQueue(List<TDDataEntity> tdDataList) {
        if (ObjectUtil.isEmpty(tdDataList)) {
            return;
        }

        int successCount = 0;
        int failCount = 0;

        for (TDDataEntity data : tdDataList) {
            try {
                boolean added = dataQueue.offer(data, 50, TimeUnit.MILLISECONDS);
                if (added) {
                    successCount++;
                } else {
                    failCount++;
                    log.warn("数据队列已满，丢弃数据: {}", data.getDevproperty());
                }
            } catch (InterruptedException e) {
                failCount++;
                log.warn("添加数据到队列被中断: {}", data.getDevproperty());
                Thread.currentThread().interrupt();
            }
        }

        log.debug("数据加入缓存队列: 成功{}条, 失败{}条, 队列当前大小: {}",
                successCount, failCount, dataQueue.size());
    }

    /**
     * 刷新缓存数据到数据库（5秒执行一次）
     */
    private void flushCachedData() {
        if (dataQueue.isEmpty()) {
            return;
        }

        batchLock.lock();
        try {
            List<TDDataEntity> batchData = new ArrayList<>();

            // 取出队列中的所有数据
            dataQueue.drainTo(batchData);

            if (!batchData.isEmpty()) {
                long startTime = System.currentTimeMillis();
                insertTDDirect(batchData);
                long endTime = System.currentTimeMillis();

                log.info("定时批量插入TDengine: 共{}条数据，耗时{}ms",
                        batchData.size(), (endTime - startTime));
            }
        } catch (Exception e) {
            log.error("定时批量插入TDengine失败: {}", e.getMessage(), e);
        } finally {
            batchLock.unlock();
        }
    }

    /**
     * 直接批量插入方法（内部使用）
     * @param tdDataList 待插入的数据列表
     */
    private void insertTDDirect(List<TDDataEntity> tdDataList) {
        if (ObjectUtil.isEmpty(tdDataList)) {
            return;
        }

        Connection conn = null;
        Statement statement = null;

        try {
            // 获取数据库连接
            conn = DruidTdDataUtil.getConnection();
            statement = conn.createStatement();
            String stableName = tDengineProperties.getStableName();

            // 构建批量插入SQL（不考虑分批，因为数据量很小）
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("INSERT INTO ");

            for (TDDataEntity tdDataEntity : tdDataList) {
                // 构建单条插入语句
                String singleInsert = String.format("%s USING %s TAGS ('%s','%s','%s') VALUES ('%s',%s) ",
                        tdDataEntity.getDevproperty(),
                        stableName,
                        tdDataEntity.getGatewayCode(),
                        tdDataEntity.getItemid(),
                        tdDataEntity.getDevproperty(),
                        tdDataEntity.getTs(),
                        tdDataEntity.getItemvalue());

                sqlBuilder.append(singleInsert);
            }

            // 执行批量插入
            String sql = sqlBuilder.toString();
            if (sql.contains("USING")) {
                statement.execute(sql);
            }

        } catch (SQLException e) {
            log.error("TDengine批量插入失败: {}", e.getMessage(), e);
            // 不抛出异常，避免影响定时任务
        } finally {
            // 确保资源正确释放
            closeResources(conn, statement, null);
        }
    }

    /**
     * 安全关闭数据库资源
     */
    private void closeResources(Connection conn, Statement statement, ResultSet resultSet) {
        try {
            if (resultSet != null) {
                resultSet.close();
            }
            if (statement != null) {
                statement.close();
            }
            if (conn != null) {
                conn.close();
            }
        } catch (SQLException e) {
            log.error("关闭数据库资源失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 根据属性编码列表和时间戳查询历史数据
     * @param dataCodeList 属性编码列表
     * @param timestamp 时间戳
     * @return 查询结果列表
     */
    public List<TDDataEntity> queryHistoryData(List<String> dataCodeList, String timestamp) {
        List<TDDataEntity> resultList = new ArrayList<>();
        Connection conn = null;
        Statement statement = null;
        ResultSet resultSet = null;

        try {
            // 1. 获取数据库连接
            conn = DruidTdDataUtil.getConnection();
            statement = conn.createStatement();

            // 2. 构建SQL查询语句
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT ts, gateway, itemid, devproperty, itemvalue FROM ")
               .append(tDengineProperties.getStableName())
               .append(" WHERE ts = '").append(timestamp).append("'")
               .append(" AND devproperty IN (");

            // 添加属性编码条件
            for (int i = 0; i < dataCodeList.size(); i++) {
                if (i > 0) {
                    sql.append(",");
                }
                sql.append("'").append(dataCodeList.get(i)).append("'");
            }
            sql.append(")");

            log.info("执行查询SQL: {}", sql);

            // 3. 执行查询
            resultSet = statement.executeQuery(sql.toString());

            // 4. 处理查询结果
            while (resultSet != null && resultSet.next()) {
                TDDataEntity entity = TDDataEntity.builder()
                    .ts(resultSet.getString("ts"))
                    .gatewayCode(resultSet.getString("gateway"))
                    .itemid(resultSet.getString("itemid"))
                    .devproperty(resultSet.getString("devproperty"))
                    .itemvalue(resultSet.getString("itemvalue"))
                    .build();

                resultList.add(entity);
            }

        } catch (SQLException e) {
            log.error("查询TDengine历史数据失败: {}", e.getMessage(), e);
            throw new ServerException(ErrorCode.INTERNAL_SERVER_ERROR);
        } finally {
            // 5. 释放资源
            closeResources(conn, statement, resultSet);
        }

        return resultList;
    }



    /**
     * 获取当前缓存队列大小（用于监控）
     * @return 队列中待处理的数据条数
     */
    public int getCacheQueueSize() {
        return dataQueue != null ? dataQueue.size() : 0;
    }

    /**
     * 获取缓存队列剩余容量（用于监控）
     * @return 队列剩余容量
     */
    public int getCacheQueueRemainingCapacity() {
        return dataQueue != null ? dataQueue.remainingCapacity() : 0;
    }

    /**
     * 手动触发缓存数据刷新（用于测试或紧急情况）
     */
    public void manualFlushCache() {
        log.info("手动触发TDengine缓存数据刷新");
        flushCachedData();
    }
}
