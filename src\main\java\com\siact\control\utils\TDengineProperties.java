package com.siact.control.utils;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @Package com.siact.subwayten.utils
 * @description:
 * @create 2024/4/2 10:54
 */

@Data
@Component
@ConfigurationProperties(prefix = "tdengine")
public class TDengineProperties {


    /**
     * 驱动
     */
    private String driverClassName;

    /**
     * url
     */
    private String url;

    /**
     * source url
     */
    private String sourceurl;
    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 初始化大小
     */
    private Integer initialSize;

    /**
     * 最大活跃数
     */
    private Integer maxActive;

    /**
     *
     */
    private Integer minIdle;
    /**
     *
     */
    private Integer maxWait;
    /**
     *
     */
    private Boolean defaultAutoCommit;

    /**
     * 原始数据超级表名
     */
    private String sourceStableName;

    /**
     * 数据超级表名
     */
    private String stableName;

    /**
     * 批量插入配置
     */
    private BatchConfig batch = new BatchConfig();

    @Data
    public static class BatchConfig {
        /**
         * 缓存队列大小
         */
        private Integer cacheQueueSize = 5000;

        /**
         * 定时刷新间隔（毫秒）- 5秒插入一次
         */
        private Long flushIntervalMs = 5000L;

        /**
         * 最大等待时间（毫秒）
         */
        private Long maxWaitTimeMs = 3000L;
    }
}
