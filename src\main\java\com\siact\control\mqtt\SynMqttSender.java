package com.siact.control.mqtt;



import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.siact.control.utils.DefaultFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;




@Slf4j
@Component
public class SynMqttSender {
    @Autowired
    IMqttSender iMqttSender;

    @Value("${mqtt.controlTimeout}")
    private int controlTimeout;

    @Value("${mqtt.readTimeout}")
    private int readTimeout;



    // 更新控制命令发送方法，提取网关信息
    public DefaultFuture sendMessage(String topic, JSONObject msg) {
        iMqttSender.sendToMqtt(topic, 0, JSON.toJSONString(msg));
        log.info("向{}主题发送消息======{}", topic, msg);

        // 从topic中提取网关ID
        // 例如: "/ARM/gateway1/V2.1.3/cmd" -> "gateway1"
        String gatewayId = extractGatewayFromTopic(topic);

        // 使用cid作为消息ID
        Long cid = msg.getLong("cid");

        // 创建Future时传入网关ID
        DefaultFuture future = new DefaultFuture(gatewayId, cid, controlTimeout);
        log.info("为控制请求创建Future: gatewayId={}, cid={}", gatewayId, cid);
        return future;
    }

    // 读取命令发送方法，使用固定ID（配合网关锁解决并发问题）
    public DefaultFuture sendReadMessage(String issueTopic, JSONObject msg) {
        iMqttSender.sendToMqtt(issueTopic, 0, JSON.toJSONString(msg));

        // 从topic中提取网关ID
        String gatewayId = issueTopic.split("/")[2];

        // 使用固定值99999L作为messageId，配合网关锁确保不会冲突
        long messageId = 99999L;

        // 创建Future时传入网关ID和固定的messageId
        DefaultFuture future = new DefaultFuture(gatewayId, messageId, readTimeout);
        log.info("为读取请求创建Future: gatewayId={}, messageId={}", gatewayId, messageId);

        return future;
    }



    // 从主题中提取网关ID
    private String extractGatewayFromTopic(String topic) {
        String[] parts = topic.split("/");
        if (parts.length >= 3) {
            return parts[2]; // /ARM/gatewayId/...
        }
        return "unknown";
    }
}
