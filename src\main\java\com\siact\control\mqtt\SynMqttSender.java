package com.siact.control.mqtt;



import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.siact.control.utils.DefaultFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicLong;


@Slf4j
@Component
public class SynMqttSender {
    @Autowired
    IMqttSender iMqttSender;

    @Value("${mqtt.controlTimeout}")
    private int controlTimeout;

    @Value("${mqtt.readTimeout}")
    private int readTimeout;

    // 用于生成唯一的读取请求ID
    private final AtomicLong readRequestIdGenerator = new AtomicLong(System.currentTimeMillis());

    // 更新控制命令发送方法，提取网关信息
    public DefaultFuture sendMessage(String topic, JSONObject msg) {
        iMqttSender.sendToMqtt(topic, 0, JSON.toJSONString(msg));
        log.info("向{}主题发送消息======{}", topic, msg);

        // 从topic中提取网关ID
        // 例如: "/ARM/gateway1/V2.1.3/cmd" -> "gateway1"
        String gatewayId = extractGatewayFromTopic(topic);

        // 使用cid作为消息ID
        Long cid = msg.getLong("cid");

        // 创建Future时传入网关ID
        DefaultFuture future = new DefaultFuture(gatewayId, cid, controlTimeout);
        log.info("为控制请求创建Future: gatewayId={}, cid={}", gatewayId, cid);
        return future;
    }

    // 更新读取命令发送方法，使用唯一ID解决并发问题
    public DefaultFuture sendReadMessage(String issueTopic, JSONObject msg) {
        // 从topic中提取网关ID
        String gatewayId = issueTopic.split("/")[2];

        // 检查消息中是否已有requestId，如果没有则生成一个
        Long requestId = msg.getLong("requestId");
        if (requestId == null) {
            requestId = generateUniqueReadId();
            msg.put("requestId", requestId);
        }

        iMqttSender.sendToMqtt(issueTopic, 0, JSON.toJSONString(msg));

        // 创建Future时使用requestId作为messageId
        DefaultFuture future = new DefaultFuture(gatewayId, requestId, readTimeout);
        log.info("为读取请求创建Future: gatewayId={}, requestId={}", gatewayId, requestId);

        return future;
    }

    /**
     * 生成唯一的读取请求ID
     * 使用时间戳+递增序列确保唯一性
     */
    private long generateUniqueReadId() {
        return readRequestIdGenerator.incrementAndGet();
    }

    // 从主题中提取网关ID
    private String extractGatewayFromTopic(String topic) {
        String[] parts = topic.split("/");
        if (parts.length >= 3) {
            return parts[2]; // /ARM/gatewayId/...
        }
        return "unknown";
    }
}
