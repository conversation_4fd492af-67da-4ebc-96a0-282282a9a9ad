spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ******************************************************************************************************************
      username: root
      password: 123456
      # driver-class-name: org.postgresql.Driver
      # url: ****************************************************************************
      # username: gbaseuser
      # password: Gbase_User@09
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      #validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true

isTest: true
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  global-config:
    db-config:
      id-type: auto

mqtt:
  username: mqttuser
  password: mqttuser
  url: tcp://192.100.30.103:1883
  in-client-id: ${random.value}
  out-client-id: ${random.value}
  clientId: ${random.int}
  defaultTopic: /ARM/+/+/result
  timeout: 100
  keepalive: 100
  willData: 100
  completionTimeout: 20
  clearSession: true
  prefixTopic: /ARM/
  suffixTopic: /V3.0.0/cmd
  # 控制命令超时时间（秒）
  controlTimeout: 5
  # 读取实时数据超时时间（秒）
  readTimeout: 5
  # 总超时时间（秒）
  totalTimeout: 8
  parallel-gateway: true

# 网关请求管理配置
gateway:
  request:
    # 网关请求超时时间（秒）
    timeoutSeconds: 8
    # 网关锁获取超时时间（秒）
    lockTimeoutSeconds: 10
    # 队列最大长度
    maxQueueSize: 100
server:
  port: 9250
siact-apiList:
  url: http://192.100.30.177:18300/bk/siact-api-mgt

#TDengine
tdengine:
  driverClassName: com.taosdata.jdbc.rs.RestfulDriver
  url: jdbc:TAOS-RS://192.100.30.103:6041/test_waq?useUnicode=true&characterEncoding=utf-8&timestampFormat=UTC
  username: root
  password: taosdata
  initialSize: 50
  maxActive: 100
  minIdle: 25
  maxWait: 60000
  defaultAutoCommit: true
  stableName: controlpoint
  # 批量插入优化配置
  batch:
    # 缓存队列大小
    cacheQueueSize: 5000
    # 定时刷新间隔（毫秒）- 5秒插入一次
    flushIntervalMs: 5000
    # 最大等待时间（毫秒）
    maxWaitTimeMs: 3000
logging:
  # 设置全局日志级别
  level:
    root: ERROR
knife4j:
  enable: true
  # 开启生产环境屏蔽
  production: false
  setting:
    enableFooter: false
    enableFooterCustom: true
    footerCustomContent: 版权所有©2024思安云创