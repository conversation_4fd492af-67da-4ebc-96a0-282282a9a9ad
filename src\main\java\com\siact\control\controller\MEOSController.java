package com.siact.control.controller;

import com.alibaba.fastjson.JSON;
import com.siact.control.entity.DataCodeValDTO;
import com.siact.control.entity.HistoryDataVO;
import com.siact.control.entity.HistoryResultVO;
import com.siact.control.entity.IssueCommandVo;

import com.siact.control.service.CommandDetailService;
import com.siact.control.service.CommonService;
import com.siact.control.service.StrategyService;
import com.siact.control.utils.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @Package com.siact.wjycontrol.controller
 * @description: 热泵机组出水温度控制
 * <AUTHOR>
 * @create 2024/6/3 17:23
 */


@Slf4j
@RestController
@RequestMapping("control/")
@Api(tags = "控制接口")
public class MEOSController {
    @Autowired
    StrategyService strategyService;
    @Autowired
    CommonService commonService;
    @Autowired
    CommandDetailService commandDetailService;
    /**
     * @return
     * <AUTHOR>
     * 控制命令下发
     * @Param JSONObject
     **/

    @ApiOperation("下发控制命令")
    @PostMapping("/issue")
    public Result commandIssue(@RequestBody IssueCommandVo issueCommandVo) {
        log.info("下发控制命令：{}", JSON.toJSONString(issueCommandVo));
        //存储命令详情
        Long commandId = commonService.saveCommand(issueCommandVo);
        //根据模型对照点位，并下发命令
        Result exeR = commonService.execute(issueCommandVo, commandId);
        //endFlag表示该命令执行结束
        boolean flagUpdate = commandDetailService.updateByCommandId(commandId, String.valueOf(exeR.getData()), 0);
        return exeR;
    }


    @ApiOperation("数据实时值查询")
    @PostMapping("/queryFeedbackVal")
    public Result<Map<String,String>> getRealTimeData(@RequestBody List<String> dataCodeList) {
        return Result.ok(commonService.getRealTimeData(dataCodeList));
    }

    @ApiOperation("控制点位历史数据查询")
    @PostMapping("/queryHistoryVal")
    public Result<HistoryResultVO> getHistoryDataVal(@RequestBody HistoryDataVO historyDataVO) {
        return Result.ok(commonService.getHistoryDataVal(historyDataVO));
    }


}
