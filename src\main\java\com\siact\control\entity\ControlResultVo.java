package com.siact.control.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("控制结果返回实体")
public class ControlResultVo {
    
    @ApiModelProperty(value = "单点控制结果", position = 1)
    private List<DeviceControlResult> issueDetail;
    
    @ApiModelProperty(value = "组合控制结果", position = 2)
    private List<List<DeviceControlResult>> multiIssueDetail;
}