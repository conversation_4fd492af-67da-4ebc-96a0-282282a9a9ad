spring:
  application:
    name: meos-control-interface
  cloud:
    nacos:
#      server-addr: 192.168.0.78:8848
#      server-addr: 192.100.30.102:8848
      server-addr: 192.168.100.211:8848
      config:
        server-addr: ${spring.cloud.nacos.server-addr}
        file-extension: yml
        prefix: ${spring.application.name}
        group: DEFAULT_GROUP
      discovery:
        server-addr: ${spring.cloud.nacos.server-addr}






