package com.siact.control.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class AsyncConfig {

    @Bean
    public AsyncTaskExecutor asyncTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 优化线程池配置以处理高频TDengine插入
        executor.setCorePoolSize(8);  // 增加核心线程数
        executor.setMaxPoolSize(16);  // 增加最大线程数
        executor.setQueueCapacity(100); // 增加队列容量
        executor.setThreadNamePrefix("TDengine-Async-");
        executor.setKeepAliveSeconds(60); // 设置线程空闲时间
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy()); // 拒绝策略
        executor.initialize();
        return executor;
    }
}